import boto3
import json

# Create a Bedrock client
client = boto3.client('bedrock-runtime', region_name='us-east-1')

# Claude 3 Nova model ID
model_id = 'amazon.nova-lite-v1:0'

# Prepare the conversation messages
messages = [
    {"role": "user", "content": "Hello, <PERSON>! Can you tell me a joke?"}
]

# Prepare the request body for the converse API
body = {
    "messages": messages,
    "temperature": 0.7
}

# Call the converse API
response = client.invoke_model(
    modelId=model_id,
    contentType='application/json',
    accept='application/json',
    body=json.dumps(body)
)

# Print the response
result = json.loads(response['body'].read())
print(result)