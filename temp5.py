import boto3
import json

# Create a Bedrock client
client = boto3.client('bedrock-runtime', region_name='us-east-1')

# AWS Nova Lite model ID
model_id = 'amazon.nova-lite-v1:0'

# System prompt (optional, can be customized)
system_prompt = "You are <PERSON> Li<PERSON>, a helpful AI assistant."

# Prepare the conversation messages
messages = [
    {
        "role": "user",
        "content": [
            {"text": "Hello, Nova Lite! Can you tell me a joke?"}
        ]
    }
]

# Prepare the request body for invoke_model
body = {
    "messages": messages,
    "inferenceConfig": {
        "maxTokens": 250,
        "temperature": 0.2
    }
}

# Call the model
try:
    response = client.invoke_model(
        modelId=model_id,
        contentType='application/json',
        accept='application/json',
        body=json.dumps(body)
    )

    # Print the response
    result = json.loads(response['body'].read())
    print("Response from Nova Lite:")
    print(json.dumps(result, indent=2))

except Exception as e:
    print(f"Error calling the model: {e}")