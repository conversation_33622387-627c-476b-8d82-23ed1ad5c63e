#!/usr/bin/env python3
"""
Simple AWS Bedrock Nova model test script.
This script demonstrates multiple ways to authenticate with AWS Bedrock.
"""

import os
import boto3
from dotenv import load_dotenv
import json

def test_with_profile():
    """Test using AWS profile (recommended for development)"""
    print("="*60)
    print("Testing with AWS Profile")
    print("="*60)
    
    try:
        # Use the profile from the existing notebook
        session = boto3.Session(profile_name="DeveloperLearningAccountAccess-************")
        client = session.client(
            service_name="bedrock-runtime",
            region_name="us-east-1"
        )
        
        # Test with Nova Micro
        model_id = "amazon.nova-micro-v1:0"
        messages = [
            {
                "role": "user", 
                "content": [
                    {
                        "text": "Hello! Tell me about AWS Bedrock in one sentence."
                    }
                ]
            }
        ]
        
        response = client.converse(
            modelId=model_id,
            messages=messages,
            inferenceConfig={
                'maxTokens': 100,
                'temperature': 0.7,
            }
        )
        
        print(f"✓ SUCCESS with profile authentication!")
        print(f"Model: {model_id}")
        print(f"Response: {response['output']['message']['content'][0]['text']}")
        print(f"Usage: {response.get('usage', 'N/A')}")
        return True
        
    except Exception as e:
        print(f"✗ Profile authentication failed: {e}")
        return False

def test_with_env_vars():
    """Test using environment variables from .env file"""
    print("\n" + "="*60)
    print("Testing with Environment Variables")
    print("="*60)
    
    try:
        load_dotenv()
        
        # Check if we have the required environment variables
        aws_access_key_id = os.getenv('AWS_ACCESS_KEY_ID')
        aws_secret_access_key = os.getenv('AWS_SECRET_ACCESS_KEY')
        aws_session_token = os.getenv('AWS_SESSION_TOKEN')
        region = os.getenv('AWS_REGION', 'us-east-1')
        
        if not aws_access_key_id or not aws_secret_access_key:
            print("✗ Missing AWS credentials in environment variables")
            return False
        
        # Create client with environment variables
        client_kwargs = {
            'service_name': 'bedrock-runtime',
            'region_name': region,
            'aws_access_key_id': aws_access_key_id.strip(),
            'aws_secret_access_key': aws_secret_access_key.strip()
        }
        
        if aws_session_token:
            client_kwargs['aws_session_token'] = aws_session_token.strip()
            print("✓ Using session token for temporary credentials")
        
        client = boto3.client(**client_kwargs)
        
        # Test with Nova Lite
        model_id = "amazon.nova-lite-v1:0"
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "text": "What are the main benefits of using AWS Bedrock? List 2 points."
                    }
                ]
            }
        ]
        
        response = client.converse(
            modelId=model_id,
            messages=messages,
            inferenceConfig={
                'maxTokens': 150,
                'temperature': 0.3,
            }
        )
        
        print(f"✓ SUCCESS with environment variable authentication!")
        print(f"Model: {model_id}")
        print(f"Response: {response['output']['message']['content'][0]['text']}")
        print(f"Usage: {response.get('usage', 'N/A')}")
        return True
        
    except Exception as e:
        print(f"✗ Environment variable authentication failed: {e}")
        return False

def test_with_default_credentials():
    """Test using default AWS credentials (IAM role, default profile, etc.)"""
    print("\n" + "="*60)
    print("Testing with Default AWS Credentials")
    print("="*60)
    
    try:
        # Use default credentials
        client = boto3.client(
            service_name="bedrock-runtime",
            region_name="us-east-1"
        )
        
        # Test with Nova Pro
        model_id = "amazon.nova-pro-v1:0"
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "text": "Write a simple Python hello world function."
                    }
                ]
            }
        ]
        
        response = client.converse(
            modelId=model_id,
            messages=messages,
            inferenceConfig={
                'maxTokens': 200,
                'temperature': 0.1,
            }
        )
        
        print(f"✓ SUCCESS with default credentials!")
        print(f"Model: {model_id}")
        print(f"Response: {response['output']['message']['content'][0]['text']}")
        print(f"Usage: {response.get('usage', 'N/A')}")
        return True
        
    except Exception as e:
        print(f"✗ Default credentials failed: {e}")
        return False

def show_credential_setup_instructions():
    """Show instructions for setting up AWS credentials"""
    print("\n" + "="*60)
    print("AWS Credentials Setup Instructions")
    print("="*60)
    print("""
To use AWS Bedrock, you need valid AWS credentials. Here are your options:

1. AWS SSO Profile (Recommended for development):
   - Run: aws sso login --profile DeveloperLearningAccountAccess-************
   - This will refresh your temporary credentials

2. Environment Variables:
   - Update the .env file with valid credentials
   - For temporary credentials (starting with ASIA), you need:
     * AWS_ACCESS_KEY_ID
     * AWS_SECRET_ACCESS_KEY  
     * AWS_SESSION_TOKEN (required for temporary credentials)

3. AWS CLI Default Profile:
   - Run: aws configure
   - Enter your access key, secret key, and region

4. IAM Role (for EC2 instances):
   - Attach an IAM role with Bedrock permissions to your EC2 instance

Current .env file status:""")
    
    load_dotenv()
    access_key = os.getenv('AWS_ACCESS_KEY_ID', 'Not set')
    secret_key = os.getenv('AWS_SECRET_ACCESS_KEY', 'Not set')
    session_token = os.getenv('AWS_SESSION_TOKEN', 'Not set')
    region = os.getenv('AWS_REGION', 'Not set')
    
    print(f"  AWS_ACCESS_KEY_ID: {'Set' if access_key != 'Not set' else 'Not set'}")
    print(f"  AWS_SECRET_ACCESS_KEY: {'Set' if secret_key != 'Not set' else 'Not set'}")
    print(f"  AWS_SESSION_TOKEN: {'Set' if session_token != 'Not set' else 'Not set'}")
    print(f"  AWS_REGION: {region}")
    
    if access_key.startswith('ASIA'):
        print("\n⚠️  Your access key starts with 'ASIA', indicating temporary credentials.")
        print("   You need to set AWS_SESSION_TOKEN in your .env file.")

def main():
    """Main function to test different authentication methods"""
    print("AWS Bedrock Nova Model Authentication Test")
    print("="*60)
    
    # Test different authentication methods
    methods = [
        ("AWS Profile", test_with_profile),
        ("Environment Variables", test_with_env_vars),
        ("Default Credentials", test_with_default_credentials)
    ]
    
    successful_methods = []
    
    for method_name, test_func in methods:
        try:
            if test_func():
                successful_methods.append(method_name)
        except Exception as e:
            print(f"✗ {method_name} test failed with exception: {e}")
    
    # Summary
    print("\n" + "="*60)
    print("Authentication Test Summary")
    print("="*60)
    
    if successful_methods:
        print("✓ Successful authentication methods:")
        for method in successful_methods:
            print(f"  - {method}")
        print(f"\n🎉 {len(successful_methods)} out of {len(methods)} methods worked!")
    else:
        print("✗ No authentication methods worked.")
        show_credential_setup_instructions()
    
    return len(successful_methods) > 0

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
