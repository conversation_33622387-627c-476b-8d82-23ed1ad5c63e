import fitz  # PyMuPDF
import os

def convert_pdf_to_jpg_high_quality(pdf_path, output_folder):
    """
    Converts a PDF file to a series of high-quality JPG images.

    Args:
        pdf_path (str): The path to the input PDF file.
        output_folder (str): The path to the folder where JPG images will be saved.
    """
    # Create the output folder if it doesn't exist
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    # Open the PDF file
    doc = fitz.open(pdf_path)

    # --- SETTINGS FOR HIGH QUALITY ---
    # Set the DPI (dots per inch). 300 is standard for high-quality printing.
    dpi = 600
    # Set the JPG quality. 100 is the maximum.
    jpg_quality = 80
    
    # Calculate the zoom factor from the DPI
    zoom = dpi / 72  # The default DPI in PDF is 72
    matrix = fitz.Matrix(zoom, zoom)

    print(f"Converting '{pdf_path}' to JPGs in '{output_folder}'...")

    # Iterate through each page of the PDF
    for page_num in range(len(doc)):
        page = doc.load_page(page_num)  # Load the page
        
        # Render the page to a pixmap (an image) using the high-res matrix
        pix = page.get_pixmap(matrix=matrix)
        
        # Define the output image path
        output_path = os.path.join(output_folder, f"page-{page_num + 1}_Quality-{jpg_quality}.jpg")
        
        # Save the pixmap as a JPG file with high quality
        pix.save(output_path, "jpeg", jpg_quality=jpg_quality)
        print(f"  - Saved page {page_num + 1}")

    doc.close()
    print("Conversion complete! ✨")

if __name__ == '__main__':
    # --- USER CONFIGURATION ---
    # 1. Path to your input PDF file
    input_pdf = "document_ai/page1_doc1_2526 TABBED APP 4 (2) (1).pdf"
    
    # 2. Path to the folder for saving the output JPG images
    output_dir = "document_ai"
    
    # Check if the PDF file exists before running
    if os.path.exists(input_pdf):
        convert_pdf_to_jpg_high_quality(input_pdf, output_dir)
    else:
        print(f"Error: The file '{input_pdf}' was not found.")

