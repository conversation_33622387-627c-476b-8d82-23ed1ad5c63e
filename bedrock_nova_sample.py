#!/usr/bin/env python3
"""
Sample script demonstrating AWS Bedrock Nova model usage with environment variables.
This script loads AWS credentials from .env file and makes calls to Nova models.
"""

import os
import boto3
from dotenv import load_dotenv
from pprint import pprint
import json

def load_environment():
    """Load environment variables from .env file"""
    load_dotenv()

    # Verify required environment variables are set
    required_vars = ['AWS_ACCESS_KEY_ID', 'AWS_SECRET_ACCESS_KEY', 'AWS_REGION']
    missing_vars = [var for var in required_vars if not os.getenv(var)]

    if missing_vars:
        raise ValueError(f"Missing required environment variables: {missing_vars}")

    print("✓ Environment variables loaded successfully")
    credentials = {
        'aws_access_key_id': os.getenv('AWS_ACCESS_KEY_ID'),
        'aws_secret_access_key': os.getenv('AWS_SECRET_ACCESS_KEY'),
        'region_name': os.getenv('AWS_REGION', 'us-east-1')
    }

    # Add session token if available (for temporary credentials)
    session_token = os.getenv('AWS_SESSION_TOKEN')
    if session_token:
        credentials['aws_session_token'] = session_token
        print("✓ Session token found and loaded")

    return credentials

def create_bedrock_client(credentials):
    """Create and return a Bedrock runtime client"""
    try:
        client_kwargs = {
            'service_name': "bedrock-runtime",
            'region_name': credentials['region_name'],
            'aws_access_key_id': credentials['aws_access_key_id'],
            'aws_secret_access_key': credentials['aws_secret_access_key']
        }

        # Add session token if available
        if 'aws_session_token' in credentials:
            client_kwargs['aws_session_token'] = credentials['aws_session_token']

        client = boto3.client(**client_kwargs)
        print("✓ Bedrock client created successfully")
        return client
    except Exception as e:
        print(f"✗ Failed to create Bedrock client: {e}")
        raise

def test_nova_micro(client):
    """Test Amazon Nova Micro model"""
    print("\n" + "="*50)
    print("Testing Amazon Nova Micro Model")
    print("="*50)
    
    model_id = "amazon.nova-micro-v1:0"
    
    messages = [
        {
            "role": "user", 
            "content": [
                {
                    "text": "Hello! Can you tell me about Amazon Bedrock in exactly 2 sentences?"
                }
            ]
        }
    ]
    
    try:
        response = client.converse(
            modelId=model_id,
            messages=messages,
            inferenceConfig={
                'maxTokens': 100,
                'temperature': 0.7,
                'topP': 0.9,
            }
        )
        
        print(f"Model: {model_id}")
        print(f"Response: {response['output']['message']['content'][0]['text']}")
        print(f"Usage: {response.get('usage', 'N/A')}")
        return True
        
    except Exception as e:
        print(f"✗ Error with Nova Micro: {e}")
        return False

def test_nova_lite(client):
    """Test Amazon Nova Lite model"""
    print("\n" + "="*50)
    print("Testing Amazon Nova Lite Model")
    print("="*50)

    model_id = "amazon.nova-lite-v1:0"

    system_prompt = [
        {
            "text": "You are a helpful AI assistant that provides concise and accurate information."
        }
    ]

    messages = [
        {
            "role": "user",
            "content": [
                {
                    "text": "Explain what AWS Bedrock is and list 3 key benefits in bullet points."
                }
            ]
        }
    ]

    try:
        response = client.converse(
            modelId=model_id,
            system=system_prompt,
            messages=messages,
            inferenceConfig={
                'maxTokens': 200,
                'temperature': 0.3,
                'topP': 0.8,
            }
        )
        
        print(f"Model: {model_id}")
        print(f"Response: {response['output']['message']['content'][0]['text']}")
        print(f"Usage: {response.get('usage', 'N/A')}")
        return True
        
    except Exception as e:
        print(f"✗ Error with Nova Lite: {e}")
        return False

def test_nova_pro(client):
    """Test Amazon Nova Pro model"""
    print("\n" + "="*50)
    print("Testing Amazon Nova Pro Model")
    print("="*50)
    
    model_id = "amazon.nova-pro-v1:0"
    
    messages = [
        {
            "role": "user",
            "content": [
                {
                    "text": "Write a brief Python function that calculates the factorial of a number using recursion. Include comments."
                }
            ]
        }
    ]
    
    try:
        response = client.converse(
            modelId=model_id,
            messages=messages,
            inferenceConfig={
                'maxTokens': 300,
                'temperature': 0.1,
                'topP': 0.9,
            }
        )
        
        print(f"Model: {model_id}")
        print(f"Response: {response['output']['message']['content'][0]['text']}")
        print(f"Usage: {response.get('usage', 'N/A')}")
        return True
        
    except Exception as e:
        print(f"✗ Error with Nova Pro: {e}")
        return False

def list_available_models(client):
    """List available models (requires bedrock client, not bedrock-runtime)"""
    print("\n" + "="*50)
    print("Attempting to list available models...")
    print("="*50)
    
    try:
        # Create a separate bedrock client for listing models
        credentials = load_environment()
        client_kwargs = {
            'service_name': "bedrock",
            'region_name': credentials['region_name'],
            'aws_access_key_id': credentials['aws_access_key_id'],
            'aws_secret_access_key': credentials['aws_secret_access_key']
        }

        # Add session token if available
        if 'aws_session_token' in credentials:
            client_kwargs['aws_session_token'] = credentials['aws_session_token']

        bedrock_client = boto3.client(**client_kwargs)
        
        response = bedrock_client.list_foundation_models()
        nova_models = [model for model in response['modelSummaries'] 
                      if 'nova' in model['modelId'].lower()]
        
        print("Available Nova models:")
        for model in nova_models:
            print(f"  - {model['modelId']}: {model.get('modelName', 'N/A')}")
            
    except Exception as e:
        print(f"✗ Could not list models: {e}")
        print("This might be due to insufficient permissions, but the runtime calls should still work.")

def main():
    """Main function to run all tests"""
    print("AWS Bedrock Nova Model Testing Script")
    print("="*50)
    
    try:
        # Load environment variables
        credentials = load_environment()
        
        # Create Bedrock client
        client = create_bedrock_client(credentials)
        
        # List available models (optional, might fail due to permissions)
        list_available_models(client)
        
        # Test different Nova models
        results = {}
        results['nova_micro'] = test_nova_micro(client)
        results['nova_lite'] = test_nova_lite(client)
        results['nova_pro'] = test_nova_pro(client)
        
        # Summary
        print("\n" + "="*50)
        print("Test Results Summary")
        print("="*50)
        for model, success in results.items():
            status = "✓ PASSED" if success else "✗ FAILED"
            print(f"{model}: {status}")
        
        successful_tests = sum(results.values())
        total_tests = len(results)
        print(f"\nOverall: {successful_tests}/{total_tests} tests passed")
        
        if successful_tests == total_tests:
            print("🎉 All Nova model tests completed successfully!")
        else:
            print("⚠️  Some tests failed. Check the error messages above.")
            
    except Exception as e:
        print(f"✗ Script failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
