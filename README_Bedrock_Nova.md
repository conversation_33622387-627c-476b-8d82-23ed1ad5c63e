# AWS Bedrock Nova Model Integration

This directory contains sample scripts for integrating with AWS Bedrock Nova models using permanent AWS credentials from a `.env` file.

## Files Created

### 1. `bedrock_nova_permanent_creds.py`
**Main testing script** that validates your AWS Bedrock setup and tests all Nova models.

**Features:**
- Tests Nova Micro, Nova Lite, and Nova Pro models
- Validates AWS credentials from `.env` file
- Shows token usage information
- Includes system prompt testing
- Comprehensive error handling

**Usage:**
```bash
python bedrock_nova_permanent_creds.py
```

### 2. `bedrock_nova_examples.py`
**Practical examples** showing common use cases for Nova models.

**Examples included:**
- Basic text generation
- Code generation with system prompts
- Creative writing with temperature control
- Data analysis and insights
- Model comparison across Nova variants
- Multi-turn conversation simulation

**Usage:**
```bash
python bedrock_nova_examples.py
```

### 3. `.env` Configuration
Updated to use permanent AWS credentials (no session tokens required).

**Required variables:**
```env
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=FmiC6arIFsm2umN5pfsJnUWzv+Gsaz8a3acSdJw9
```

## Nova Models Available

### Amazon Nova Micro (`amazon.nova-micro-v1:0`)
- **Best for:** Simple tasks, quick responses
- **Cost:** Most economical
- **Use cases:** Basic Q&A, simple text generation

### Amazon Nova Lite (`amazon.nova-lite-v1:0`)
- **Best for:** Balanced performance and cost
- **Use cases:** Content creation, analysis, general chat

### Amazon Nova Pro (`amazon.nova-pro-v1:0`)
- **Best for:** Complex tasks requiring higher reasoning
- **Use cases:** Code generation, detailed analysis, complex problem solving

## Key Features Demonstrated

### 1. Basic Model Interaction
```python
from bedrock_nova_examples import BedrockNovaClient

client = BedrockNovaClient()
response = client.chat("Hello, how are you?")
print(response)
```

### 2. System Prompts
```python
response = client.chat(
    message="Write a Python function",
    system_prompt="You are an expert Python developer",
    model="amazon.nova-pro-v1:0"
)
```

### 3. Temperature Control
```python
# More creative (higher temperature)
creative_response = client.chat(
    message="Write a story",
    temperature=0.9
)

# More focused (lower temperature)
factual_response = client.chat(
    message="Explain a concept",
    temperature=0.1
)
```

## Prerequisites

### Dependencies
```bash
pip install boto3 python-dotenv
```

### AWS Permissions
Your AWS credentials need the following permissions:
- `bedrock:InvokeModel`
- `bedrock:InvokeModelWithResponseStream` (for streaming)

## Test Results

When you run `bedrock_nova_permanent_creds.py`, you should see:

```
AWS Bedrock Nova Model Test (Permanent Credentials)
============================================================
✓ AWS credentials loaded from .env file
✓ Using region: us-east-1
✓ Access Key ID: AKIAT544...
✓ Bedrock client created successfully

==================================================
Testing Amazon Nova Micro Model
==================================================
✓ Model: amazon.nova-micro-v1:0
✓ Response: [Model response here]
✓ Input tokens: 16
✓ Output tokens: 46
✓ Total tokens: 62

[... similar for other models ...]

============================================================
Test Results Summary
============================================================
Nova Micro: ✓ PASSED
Nova Lite: ✓ PASSED
Nova Pro: ✓ PASSED
System Prompt: ✓ PASSED

Overall: 4/4 tests passed
🎉 All Nova model tests completed successfully!
```

## Troubleshooting

### Common Issues

1. **Invalid credentials error:**
   - Check that AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY are correct in `.env`
   - Ensure no extra spaces in the credential values

2. **Region not supported:**
   - Nova models are available in specific regions (us-east-1, us-west-2, etc.)
   - Update AWS_REGION in `.env` if needed

3. **Model not found:**
   - Ensure you have access to Nova models in your AWS account
   - Some models may require special access or be in preview

4. **Permission denied:**
   - Your AWS user/role needs `bedrock:InvokeModel` permission
   - Contact your AWS administrator to grant Bedrock permissions

## Next Steps

1. **Integrate into your application:** Use the `BedrockNovaClient` class as a starting point
2. **Experiment with parameters:** Try different temperature, max_tokens, and topP values
3. **Add streaming:** Implement streaming responses for real-time applications
4. **Error handling:** Add robust error handling for production use
5. **Monitoring:** Add logging and metrics for production deployments

## Cost Optimization Tips

1. **Choose the right model:** Use Nova Micro for simple tasks, Pro for complex ones
2. **Limit max_tokens:** Set appropriate limits to control costs
3. **Cache responses:** Cache common responses to reduce API calls
4. **Monitor usage:** Track token usage to optimize costs

---

✅ **Status:** All scripts tested and working with permanent AWS credentials
🔧 **Last Updated:** 2025-08-11
