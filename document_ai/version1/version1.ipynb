{"cells": [{"cell_type": "code", "execution_count": 2, "id": "7f9c4342", "metadata": {}, "outputs": [], "source": ["os.getenv(\"ENVIRONMENT\")"]}, {"cell_type": "code", "execution_count": null, "id": "be288041", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Attempting to automatically open the SSO authorization page in your default browser.\n", "If the browser does not open or you wish to use a different device to authorize this request, open the following URL:\n", "\n", "https://oidc.us-east-1.amazonaws.com/authorize?response_type=code&client_id=ue5vMoYFlPDKWE5sPC3hznVzLWVhc3QtMQ&redirect_uri=http%3A%2F%2F127.0.0.1%3A44955%2Foauth%2Fcallback&state=8f7160c2-427f-49f9-b21d-806050c1f7a3&code_challenge_method=S256&scopes=sso%3Aaccount%3Aaccess&code_challenge=T0fDxHRTuNFQRz_VEq0hWIQ20WGiBcoeIfPXStFaEgQ\n", "Successfully logged into Start URL: https://armakuni-us.awsapps.com/start\n"]}], "source": ["!aws sso login --profile DeveloperLearningAccountAccess-************\n", "\n", "import boto3, json\n", "from pprint import pprint\n", "\n", "# session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "client = boto3.client(\"bedrock-runtime\", \n", "                      region_name=\"us-east-1\",\n", "                      )"]}, {"cell_type": "code", "execution_count": 15, "id": "7a3ff928", "metadata": {}, "outputs": [], "source": ["import boto3, json\n", "from pprint import pprint\n", "\n", "session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "client = session.client(\"bedrock-runtime\", region_name=\"us-east-1\")"]}, {"cell_type": "code", "execution_count": 12, "id": "4fc71c23", "metadata": {}, "outputs": [], "source": ["# system prompt\n", "\n", "system_prompt = [{\"text\": '''\n", "You are very helpful assistant which extracts data in key-value pair from given document. To achieve that follow below steps\n", "\n", "Step 1:\n", "Read the data given to you in user prompt in following structure:\n", "=== NON-TABLE TEXT ===\n", "<non-table-text>\n", "\n", "=== TABLE 1 ===\n", "<table-1-data>\n", "=== TABLE 2 ===\n", "<table-2-data>\n", "...\n", "=== TABLE n ===\n", "<table-n-data>\n", "\n", "Step 2:\n", "Refer to image for location of given data and it's context\n", "\n", "Step 3:\n", "Extract complete data in key-value pair from given data. \n", "\n", "Step 4:\n", "Once again make sure that each and every item is covered in extracted key-value pairs, If not then include it in key-value pair.\n", "\n", "Step 5:\n", "Use tool call to with extracted data\n", "'''}]"]}, {"cell_type": "code", "execution_count": 13, "id": "3e6a399d", "metadata": {}, "outputs": [], "source": ["# user prompt\n", "# text\n", "\n", "with open('structured_text.json', 'r') as f:\n", "    structured_text = json.load(f)\n", "\n", "# image\n", "\n", "image_path = \"document.jpeg\"\n", "\n", "with open(image_path, \"rb\") as f:\n", "    image_bytes = f.read()\n", "    \n", "image_format = image_path.split(\".\")[-1].lower()\n", "\n", "messages = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": [\n", "            {\"text\": \"Analyze the provided image and describe its contents.\"},\n", "            {\"text\": structured_text},\n", "            {\"image\": {\"format\": image_format, \"source\": {\"bytes\": image_bytes}}}\n", "        ]\n", "    }\n", "]"]}, {"cell_type": "code", "execution_count": 10, "id": "eb6fba81", "metadata": {}, "outputs": [], "source": ["# response format\n", "\n", "toolConfig={\n", "    \"tools\": [\n", "        {\n", "            'toolSpec': {\n", "                'name': 'All_details',\n", "                'description': 'This tool received everything present in given image as key value pairs',\n", "                'inputSchema': {\n", "                    'json': {\n", "                        'title': 'Details',\n", "                        'type': 'object',\n", "                        'properties': {\n", "                            'content': {\n", "                                'title': 'Content',\n", "                                'type': 'string',\n", "                                'description': 'content inside the image in key-value pair',\n", "                                \"additionalProperties\": {\n", "                                        \"type\": \"string\"\n", "                                    }\n", "                            }\n", "                        },\n", "                        'required': [\n", "                            'content'\n", "                        ]\n", "                    }\n", "                },\n", "            }\n", "        }\n", "    ],\n", "    \"toolChoice\": {\n", "        \"tool\":{\"name\":\"All_details\"}\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 11, "id": "da7cc66a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'Agency Customer ID': 'MERCLAN-C2',\n", " 'Attachments Available': 'Accounts Receivable/Valuable Papers, Additional '\n", "                          'Interest Schedule, Additional Premises Information '\n", "                          'Schedule, Apartment Building Supplement, Condo Assn '\n", "                          'Bylaws, Contractors Supplement, Coverages Schedule, '\n", "                          'Dealers Section, Driver Information Schedule, '\n", "                          'Electronic Data Processing Section, Glass and Sign '\n", "                          'Section, Hotel/Motel Supplement, '\n", "                          'Installation/Builders Risk Section, International '\n", "                          'Liability Exposure Supplement, International '\n", "                          'Property Exposure Supplement, Loss Summary, Open '\n", "                          'Cargo Section, Premium Payment Supplement, '\n", "                          'Professional Liability Supplement, '\n", "                          'Restaurant/Tavern Supplement, Statement/Schedule of '\n", "                          'Values, State Supplement, Vacant Building '\n", "                          'Supplement, Vehicle Schedule',\n", " 'Billing Plan': 'DIRECT',\n", " 'Carrier': 'N/A',\n", " 'Copyright': '© 2022 ACORD CORPORATION. All rights reserved.',\n", " 'Date': '05/20/2025',\n", " 'Document Type': 'ACORD California Commercial Insurance Application',\n", " 'Entity Type Options': 'Corporation, Individual, Joint Venture, LLC, Not for '\n", "                        'Profit Org, Partnership, Subchapter S Corporation, '\n", "                        'Trust',\n", " 'First Named Insured': 'Merchants Landscape Services, Inc.',\n", " 'First Named Insured Address': '1190 Monterey Pass Road, Monterey Park, CA '\n", "                                '91754',\n", " 'First Named Insured FEIN': '95-4725606',\n", " 'First Named Insured GL Code': '0782',\n", " 'First Named Insured NAICS': '561730',\n", " 'First Named Insured Phone': '(*************',\n", " 'First Named Insured SIC': '561730',\n", " 'First Named Insured Website': 'https://www.merchantslandscape.com/',\n", " 'Form Number': 'ACORD 125 CA (2023/01)',\n", " 'Lines of Business - Boiler & Machinery': '$',\n", " 'Lines of Business - Business Auto': '$',\n", " 'Lines of Business - Business Owners': '$',\n", " 'Lines of Business - Commercial General Liability': '$',\n", " 'Lines of Business - Commercial Inland Marine': '$',\n", " 'Lines of Business - Commercial Property': '$',\n", " 'Lines of Business - Crime': '$',\n", " 'Lines of Business - Cyber and Privacy': '$',\n", " 'Lines of Business - Fiduciary Liability': '$',\n", " 'Lines of Business - Garage and Dealers': '$',\n", " 'Lines of Business - Liquor Liability': '$',\n", " 'Lines of Business - Motor Carrier': '$',\n", " 'Lines of Business - Truckers': '$',\n", " 'Lines of Business - Umbrella': '$',\n", " 'Lines of Business - Yacht': '$',\n", " 'Marketing Company': 'USE ONLY WHEN CARRIER IS TBD',\n", " 'NAIC Code': 'N/A',\n", " 'Page': 'Page 1 of 4',\n", " 'Payment Plan': 'MO',\n", " 'Policy Number': 'TBD',\n", " 'Producer': 'IMA, Inc. - Pasadena',\n", " 'Producer Address': '3475 E. Foothill Boulevard, Suite 100, Pasadena, CA '\n", "                     '91107',\n", " 'Producer Fax': '(*************',\n", " 'Producer Phone': '(*************',\n", " 'Proposed Effective Date': '07/01/2025',\n", " 'Proposed Expiration Date': '07/01/2026',\n", " 'Second Named Insured': 'Merchants Landscape Services, Inc.',\n", " 'Second Named Insured Address': '1190 Monterey Pass Road, Monterey Park, CA '\n", "                                 '91754',\n", " 'Second Named Insured Description': 'Commercial contractor landscaper, no r',\n", " 'Second Named Insured FEIN': '95-4725606',\n", " 'Second Named Insured GL Code': '97047',\n", " 'Second Named Insured NAICS': '561730',\n", " 'Second Named Insured Phone': '(************* 609',\n", " 'Second Named Insured SIC': '0782',\n", " 'Status of Transaction Options': 'QUOTE, ISSUE POLICY, RENEW, BOUND, CHANGE, '\n", "                                  'CANCEL',\n", " 'Third Named Insured': 'Merchants Landscaping, Inc.',\n", " 'Third Named Insured Address': '1190 Monterey Pass Road, Monterey Park, CA '\n", "                                '91754',\n", " 'Trademark Notice': 'The ACORD name and logo are registered marks of ACORD'}\n"]}], "source": ["# main api call\n", "\n", "response = client.converse(\n", "    modelId=\"arn:aws:bedrock:us-east-1:************:inference-profile/us.anthropic.claude-sonnet-4-20250514-v1:0\",\n", "    system=system_prompt,\n", "    messages=messages,\n", "    inferenceConfig={\n", "        'maxTokens': 4096,\n", "        'temperature': 0,\n", "        'topP': 1,\n", "    },\n", "    toolConfig=toolConfig,\n", ")\n", "\n", "output = response['output']['message']['content'][0]['toolUse']['input']\n", "pprint(json.loads(output[\"content\"]))"]}, {"cell_type": "code", "execution_count": null, "id": "b52b8558", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}