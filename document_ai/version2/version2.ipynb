# load .env
import os
from dotenv import load_dotenv
load_dotenv()

import boto3, json
from pprint import pprint

# Option 1: Use AWS profile (recommended for automatic credential refresh)
# session = boto3.Session(profile_name="DeveloperLearningAccountAccess-************")
# client = session.client("bedrock-runtime", region_name="us-east-1")

# Option 2: Use environment variables (current approach)
client = boto3.client("bedrock-runtime", 
                      region_name="us-east-1", 
                      aws_access_key_id=os.environ.get("AWS_ACCESS_KEY_ID"), 
                      aws_secret_access_key=os.environ.get("AWS_SECRET_ACCESS_KEY"),
                      aws_session_token=os.environ.get("AWS_SESSION_TOKEN"))

# system prompt

system_prompt = [{"text": '''
You are very helpful assistant which extracts data in key-value pair from given document. To achieve that follow below steps

Step 1:
Read the data given to you in user prompt in following structure:
=== NON-TABLE TEXT ===
<non-table-text>

=== TABLE 1 ===
<table-1-data>
=== TABLE 2 ===
<table-2-data>
...
=== TABLE n ===
<table-n-data>

Step 2:
Refer to image for location of given data and it's context

Step 3:
Extract complete data in key-value pair from given data. 

Step 4:
Once again make sure that each and every item is covered in extracted key-value pairs, If not then include it in key-value pair.

Step 5:
Use tool call to with extracted data
'''}]

# user prompt
# text

with open('structured_text.json', 'r') as f:
    structured_text = json.load(f)

# image

image_path = "document.jpeg"

with open(image_path, "rb") as f:
    image_bytes = f.read()
    
image_format = image_path.split(".")[-1].lower()

messages = [
    {
        "role": "user",
        "content": [
            {"text": "Analyze the provided image and describe its contents."},
            {"text": structured_text},
            {"image": {"format": image_format, "source": {"bytes": image_bytes}}}
        ]
    }
]

# response format

toolConfig={
    "tools": [
        {
            'toolSpec': {
                'name': 'All_details',
                'description': 'This tool received everything present in given image as key value pairs',
                'inputSchema': {
                    'json': {
                        'title': 'Details',
                        'type': 'object',
                        'properties': {
                            'content': {
                                'title': 'Content',
                                'type': 'string',
                                'description': 'content inside the image in key-value pair',
                                "additionalProperties": {
                                        "type": "string"
                                    }
                            }
                        },
                        'required': [
                            'content'
                        ]
                    }
                },
            }
        }
    ],
    "toolChoice": {
        "tool":{"name":"All_details"}
    }
}

# main api call

response = client.converse(
    modelId="amazon.nova-lite-v1:0",
    system=system_prompt,
    messages=messages,
    inferenceConfig={
        'maxTokens': 4096,
        'temperature': 0,
        'topP': 1,
    },
    toolConfig=toolConfig,
)

output = response['output']['message']['content'][0]['toolUse']['input']
pprint(json.loads(output["content"]))

