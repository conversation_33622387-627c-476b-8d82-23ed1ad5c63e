{"cells": [{"cell_type": "markdown", "id": "00061fb7", "metadata": {}, "source": ["# Login to AWS"]}, {"cell_type": "code", "execution_count": 1, "id": "9b4782c3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Attempting to automatically open the SSO authorization page in your default browser.\n", "If the browser does not open or you wish to use a different device to authorize this request, open the following URL:\n", "\n", "https://oidc.us-east-1.amazonaws.com/authorize?response_type=code&client_id=ue5vMoYFlPDKWE5sPC3hznVzLWVhc3QtMQ&redirect_uri=http%3A%2F%2F127.0.0.1%3A39657%2Foauth%2Fcallback&state=be39eeb9-af5e-4d14-a787-53b31bf2311e&code_challenge_method=S256&scopes=sso%3Aaccount%3Aaccess&code_challenge=iiucaW09Y2_xBS7f2GNmvRVrZAqsZVAIOjLuB-sbBfM\n", "Successfully logged into Start URL: https://armakuni-us.awsapps.com/start\n"]}], "source": ["# to login to AWS : \n", "!aws sso login --profile DeveloperLearningAccountAccess-************\n", "\n", "from pprint import pprint"]}, {"cell_type": "markdown", "id": "175f6269", "metadata": {}, "source": ["# Textract Call"]}, {"cell_type": "code", "execution_count": null, "id": "2816bb40", "metadata": {}, "outputs": [], "source": ["import boto3\n", "\n", "session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "\n", "client = session.client(\n", "    service_name=\"textract\", \n", "    region_name=\"us-east-1\"  # use the region where Bedrock is enabled\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "78cdfdee", "metadata": {}, "outputs": [], "source": ["document_path = \"page1_doc1_2526 TABBED APP 4 (2) (1).pdf\"\n", "\n", "# Read document bytes\n", "with open(document_path, 'rb') as doc:\n", "    image_bytes = doc.read()\n", "\n", "# Call Textract with tables\n", "response = client.analyze_document(\n", "    Document={'Bytes': image_bytes},\n", "    FeatureTypes=['TABLES']\n", ")"]}, {"cell_type": "code", "execution_count": 166, "id": "dd29725e", "metadata": {}, "outputs": [], "source": ["# saving response\n", "import json\n", "\n", "with open(f'{document_path.split(\".\")[0]}.json', 'w') as f:\n", "    json.dump(response, f)"]}, {"cell_type": "code", "execution_count": null, "id": "98c15045", "metadata": {}, "outputs": [], "source": ["def extract_text_from_textract_response(textract_response):\n", "    \"\"\"\n", "    Extract text from AWS Textract response in two formats:\n", "    1. Simple extracted text (all text concatenated)\n", "    2. Structured format with non-table text and tables separately\n", "    \n", "    Args:\n", "        textract_response: AWS Textract response dictionary\n", "    \n", "    Returns:\n", "        tuple: (simple_text, structured_output)\n", "    \"\"\"   \n", "    import csv\n", "    from io import StringIO\n", "    \n", "    blocks = textract_response.get('Blocks', [])\n", "    \n", "    # Create dictionaries for quick lookup\n", "    block_map = {block['Id']: block for block in blocks}\n", "    \n", "    # Separate blocks by type\n", "    line_blocks = [block for block in blocks if block.get('BlockType') == 'LINE']\n", "    table_blocks = [block for block in blocks if block.get('BlockType') == 'TABLE']\n", "    cell_blocks = [block for block in blocks if block.get('BlockType') == 'CELL']\n", "    \n", "    # 1. Simple extracted text - all LINE blocks concatenated\n", "    simple_text = '\\n'.join([block.get('Text', '') for block in line_blocks if block.get('Text')])\n", "    \n", "    # 2. Structured format\n", "    structured_output = \"\"\n", "    \n", "    # Get table cell IDs to exclude from non-table text\n", "    table_cell_ids = set()\n", "    for table in table_blocks:\n", "        if 'Relationships' in table:\n", "            for relationship in table['Relationships']:\n", "                if relationship['Type'] == 'CHILD':\n", "                    table_cell_ids.update(relationship['Ids'])\n", "    \n", "    # Get all text that belongs to table cells\n", "    table_text_ids = set()\n", "    for cell_id in table_cell_ids:\n", "        if cell_id in block_map and 'Relationships' in block_map[cell_id]:\n", "            for relationship in block_map[cell_id]['Relationships']:\n", "                if relationship['Type'] == 'CHILD':\n", "                    table_text_ids.update(relationship['Ids'])\n", "    \n", "    # 2.a. Non-table text\n", "    non_table_lines = []\n", "    for block in line_blocks:\n", "        if block.get('Text') and block['Id'] not in table_text_ids:\n", "            non_table_lines.append(block['Text'])\n", "    \n", "    structured_output += \"=== NON-TABLE TEXT ===\\n\"\n", "    structured_output += '\\n'.join(non_table_lines)\n", "    structured_output += \"\\n\"\n", "    \n", "    # 2.b. Tables in CSV format\n", "    for table_idx, table in enumerate(table_blocks, 1):\n", "        structured_output += f\"\\n=== TABLE {table_idx} ===\\n\"\n", "        \n", "        # Build table structure\n", "        table_data = {}\n", "        max_row = 0\n", "        max_col = 0\n", "        \n", "        if 'Relationships' in table:\n", "            for relationship in table['Relationships']:\n", "                if relationship['Type'] == 'CHILD':\n", "                    for cell_id in relationship['Ids']:\n", "                        if cell_id in block_map:\n", "                            cell = block_map[cell_id]\n", "                            if cell.get('BlockType') == 'CELL':\n", "                                row_index = cell.get('RowIndex', 1) - 1  # Convert to 0-based\n", "                                col_index = cell.get('ColumnIndex', 1) - 1  # Convert to 0-based\n", "                                \n", "                                max_row = max(max_row, row_index)\n", "                                max_col = max(max_col, col_index)\n", "                                \n", "                                # Get cell text\n", "                                cell_text = \"\"\n", "                                if 'Relationships' in cell:\n", "                                    for cell_rel in cell['Relationships']:\n", "                                        if cell_rel['Type'] == 'CHILD':\n", "                                            for word_id in cell_rel['Ids']:\n", "                                                if word_id in block_map:\n", "                                                    word_block = block_map[word_id]\n", "                                                    if word_block.get('Text'):\n", "                                                        cell_text += word_block['Text'] + \" \"\n", "                                \n", "                                table_data[(row_index, col_index)] = cell_text.strip()\n", "        \n", "        # Convert to CSV format\n", "        if table_data:\n", "            csv_output = StringIO()\n", "            csv_writer = csv.writer(csv_output)\n", "            \n", "            for row in range(max_row + 1):\n", "                row_data = []\n", "                for col in range(max_col + 1):\n", "                    cell_value = table_data.get((row, col), \"\")\n", "                    row_data.append(cell_value)\n", "                csv_writer.writerow(row_data)\n", "            \n", "            structured_output += csv_output.getvalue()\n", "        else:\n", "            structured_output += \"No table data found\"\n", "        \n", "        structured_output += \"\"\n", "    \n", "    return simple_text, structured_output"]}, {"cell_type": "code", "execution_count": 168, "id": "test_extraction", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== SIMPLE EXTRACTED TEXT ===\n", "CHOSPEDALES\n", "ACORD®\n", "CALIFORNIA COMMERCIAL INSURANCE APPLICATION\n", "DATE (MM/DD/YYYY)\n", "APPLICANT INFORMATION SECTION\n", "05/20/2025\n", "PRODUCER\n", "CARRIER\n", "NAIC CODE\n", "IMA, Inc. - Pasadena\n", "MARKETING COMPANY - USE ONLY WHEN CARRIER IS TBD\n", "N/A\n", "3475 E. Foothill Boulevard\n", "Suite 100\n", "COMPANY POLICY OR PROGRAM NAME\n", "PROGRAM CODE\n", "Pasadena, CA 91107\n", "POLICY NUMBER\n", "TBD\n", "CONTACT\n", "NAME:\n", "UNDERWRITER\n", "UNDERWRITER OFFICE\n", "PHONE\n", "(A/C, No, Ext):\n", "**************\n", "FAX\n", "(A/C, No):\n", "**************\n", "QUOTE\n", "ISSUE POLICY\n", "RENEW\n", "E-MAIL\n", "STATUS OF\n", "ADDRESS:\n", "TRANSACTION\n", "BOUND (Give Date and/or Attach Copy):\n", "CODE:\n", "SUBCODE:\n", "CHANGE\n", "DATE\n", "TIME\n", "AM\n", "AGENCY CUSTOMER ID: MERCLAN-C2\n", "CANCEL\n", "PM\n", "LINES OF BUSINESS\n", "INDICATE LINES OF BUSINESS\n", "PREMIUM\n", "PREMIUM\n", "PREMIUM\n", "BOILER & MACHINERY\n", "$\n", "CYBER AND PRIVACY\n", "$\n", "YACHT\n", "$\n", "BUSINESS AUTO\n", "$\n", "FIDUCIARY LIABILITY\n", "$\n", "$\n", "BUSINESS OWNERS\n", "$\n", "GARAGE AND DEALERS\n", "$\n", "$\n", "COMMERCIAL GENERAL LIABILITY\n", "$\n", "LIQUOR LIABILITY\n", "$\n", "$\n", "COMMERCIAL INLAND MARINE\n", "$\n", "MOTOR CARRIER\n", "$\n", "$\n", "COMMERCIAL PROPERTY\n", "$\n", "TRUCKERS\n", "$\n", "$\n", "CRIME\n", "$\n", "UMBRELLA\n", "$\n", "$\n", "ATTACHMENTS\n", "ACCOUNTS RECEIVABLE / VALUABLE PAPERS\n", "GLASS AND SIGN SECTION\n", "STATEMENT / SCHEDULE OF VALUES\n", "ADDITIONAL INTEREST SCHEDULE\n", "HOTEL / MOTEL SUPPLEMENT\n", "STATE SUPPLEMENT (If applicable)\n", "ADDITIONAL PREMISES INFORMATION SCHEDULE\n", "INSTALLATION / BUILDERS RISK SECTION\n", "VACANT BUILDING SUPPLEMENT\n", "APARTMENT BUILDING SUPPLEMENT\n", "INTERNATIONAL LIABILITY EXPOSURE SUPPLEMENT\n", "VEHICLE SCHEDULE\n", "CONDO ASSN BYLAWS (for D&O Coverage only)\n", "INTERNATIONAL PROPERTY EXPOSURE SUPPLEMENT\n", "CONTRACTORS SUPPLEMENT\n", "LOSS SUMMARY\n", "COVERAGES SCHEDULE\n", "OPEN CARGO SECTION\n", "DEALERS SECTION\n", "PREMIUM PAYMENT SUPPLEMENT\n", "DRIVER INFORMATION SCHEDULE\n", "PROFESSIONAL LIABILITY SUPPLEMENT\n", "ELECTRONIC DATA PROCESSING SECTION\n", "RESTAURANT / TAVERN SUPPLEMENT\n", "POLICY INFORMATION\n", "MINIMUM\n", "PROPOSED EFF DATE\n", "PROPOSED EXP DATE\n", "BILLING PLAN\n", "PAYMENT PLAN\n", "METHOD OF PAYMENT\n", "AUDIT\n", "DEPOSIT\n", "PREMIUM\n", "POLICY PREMIUM\n", "07/01/2025\n", "07/01/2026\n", "AGENCY\n", "MO\n", "$\n", "$\n", "$\n", "DIRECT\n", "APPLICANT INFORMATION\n", "NAME (First Named Insured) AND MAILING ADDRESS (including ZIP+4)\n", "GL CODE\n", "SIC\n", "NAICS\n", "FEIN OR SOC SEC #\n", "Merchants Landscape Services, Inc.\n", "0782\n", "561730\n", "95-4725606\n", "1190 Monterey Pass Road\n", "BUSINESS\n", "PHONE\n", "#: (*************\n", "Monterey Park, CA 91754\n", "WEBSITE ADDRESS\n", "https://www.merchantslandscape.com/\n", "CORPORATION\n", "JOINT VENTURE\n", "NOT FOR PROFIT ORG\n", "SUBCHAPTER \"S\" CORPORATION\n", "NO. OF MEMBERS\n", "INDIVIDUAL\n", "LLC\n", "AND MANAGERS:\n", "PARTNERSHIP\n", "TRUST\n", "Client\n", "NAME (Other Named Insured) AND MAILING ADDRESS (including ZIP+4)\n", "GL CODE\n", "SIC\n", "NAICS\n", "FEIN OR SOC SEC #\n", "Merchants Landscape Services, Inc.\n", "97047\n", "0782\n", "561730\n", "95-4725606\n", "1190 Monterey Pass Road\n", "Monterey Park, CA 91754\n", "BUSINESS PHONE #:\n", "************** 609\n", "WEBSITE ADDRESS\n", "CORPORATION\n", "JOINT VENTURE\n", "NOT FOR PROFIT ORG\n", "SUBCHAPTER \"S\" CORPORATION\n", "Commercial contractor landscaper, no r\n", "NO. OF MEMBERS\n", "INDIVIDUAL\n", "LLC\n", "AND MANAGERS:\n", "PARTNERSHIP\n", "TRUST\n", "NAME (Other Named Insured) AND MAILING ADDRESS (including ZIP+4)\n", "GL CODE\n", "SIC\n", "NAICS\n", "FEIN OR SOC SEC #\n", "Merchants Landscaping, Inc.\n", "1190 Monterey Pass Road\n", "Monterey Park, CA 91754\n", "BUSINESS PHONE #:\n", "WEBSITE ADDRESS\n", "CORPORATION\n", "JOINT VENTURE\n", "NOT FOR PROFIT ORG\n", "SUBCHAPTER \"S\" CORPORATION\n", "INDIVIDUAL\n", "LLC\n", "NO. OF MEMBERS\n", "PARTNERSHIP\n", "TRUST\n", "AND MANAGERS:\n", "ACORD 125 CA (2023/01)\n", "Page 1 of 4\n", "© 2022 ACORD CORPORATION. All rights reserved.\n", "The ACORD name and logo are registered marks of ACORD\n", "==================================================\n"]}], "source": ["# Test the function with the response\n", "simple_text, structured_text = extract_text_from_textract_response(response)\n", "\n", "print(\"=== SIMPLE EXTRACTED TEXT ===\")\n", "print(simple_text)\n", "print(\"\" + \"=\"*50 + \"\")"]}, {"cell_type": "code", "execution_count": 169, "id": "80e29b01", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== STRUCTURED EXTRACTED TEXT ===\n", "=== NON-TABLE TEXT ===\n", "CHOSPEDALES\n", "ACORD®\n", "CALIFORNIA COMMERCIAL INSURANCE APPLICATION\n", "DATE (MM/DD/YYYY)\n", "APPLICANT INFORMATION SECTION\n", "05/20/2025\n", "PRODUCER\n", "CARRIER\n", "NAIC CODE\n", "IMA, Inc. - Pasadena\n", "MARKETING COMPANY - USE ONLY WHEN CARRIER IS TBD\n", "N/A\n", "3475 E. Foothill Boulevard\n", "Suite 100\n", "COMPANY POLICY OR PROGRAM NAME\n", "PROGRAM CODE\n", "Pasadena, CA 91107\n", "POLICY NUMBER\n", "TBD\n", "CONTACT\n", "NAME:\n", "UNDERWRITER\n", "UNDERWRITER OFFICE\n", "PHONE\n", "(A/C, No, Ext):\n", "**************\n", "FAX\n", "(A/C, No):\n", "**************\n", "QUOTE\n", "ISSUE POLICY\n", "RENEW\n", "E-MAIL\n", "STATUS OF\n", "ADDRESS:\n", "TRANSACTION\n", "BOUND (Give Date and/or Attach Copy):\n", "CODE:\n", "SUBCODE:\n", "CHANGE\n", "DATE\n", "TIME\n", "AM\n", "AGENCY CUSTOMER ID: MERCLAN-C2\n", "CANCEL\n", "PM\n", "LINES OF BUSINESS\n", "INDICATE LINES OF BUSINESS\n", "PREMIUM\n", "PREMIUM\n", "PREMIUM\n", "BOILER & MACHINERY\n", "$\n", "CYBER AND PRIVACY\n", "$\n", "YACHT\n", "$\n", "BUSINESS AUTO\n", "$\n", "FIDUCIARY LIABILITY\n", "$\n", "$\n", "BUSINESS OWNERS\n", "$\n", "GARAGE AND DEALERS\n", "$\n", "$\n", "COMMERCIAL GENERAL LIABILITY\n", "$\n", "LIQUOR LIABILITY\n", "$\n", "$\n", "COMMERCIAL INLAND MARINE\n", "$\n", "MOTOR CARRIER\n", "$\n", "$\n", "COMMERCIAL PROPERTY\n", "$\n", "TRUCKERS\n", "$\n", "$\n", "CRIME\n", "$\n", "UMBRELLA\n", "$\n", "$\n", "ATTACHMENTS\n", "ACCOUNTS RECEIVABLE / VALUABLE PAPERS\n", "GLASS AND SIGN SECTION\n", "STATEMENT / SCHEDULE OF VALUES\n", "ADDITIONAL INTEREST SCHEDULE\n", "HOTEL / MOTEL SUPPLEMENT\n", "STATE SUPPLEMENT (If applicable)\n", "ADDITIONAL PREMISES INFORMATION SCHEDULE\n", "INSTALLATION / BUILDERS RISK SECTION\n", "VACANT BUILDING SUPPLEMENT\n", "APARTMENT BUILDING SUPPLEMENT\n", "INTERNATIONAL LIABILITY EXPOSURE SUPPLEMENT\n", "VEHICLE SCHEDULE\n", "CONDO ASSN BYLAWS (for D&O Coverage only)\n", "INTERNATIONAL PROPERTY EXPOSURE SUPPLEMENT\n", "CONTRACTORS SUPPLEMENT\n", "LOSS SUMMARY\n", "COVERAGES SCHEDULE\n", "OPEN CARGO SECTION\n", "DEALERS SECTION\n", "PREMIUM PAYMENT SUPPLEMENT\n", "DRIVER INFORMATION SCHEDULE\n", "PROFESSIONAL LIABILITY SUPPLEMENT\n", "ELECTRONIC DATA PROCESSING SECTION\n", "RESTAURANT / TAVERN SUPPLEMENT\n", "POLICY INFORMATION\n", "MINIMUM\n", "PROPOSED EFF DATE\n", "PROPOSED EXP DATE\n", "BILLING PLAN\n", "PAYMENT PLAN\n", "METHOD OF PAYMENT\n", "AUDIT\n", "DEPOSIT\n", "PREMIUM\n", "POLICY PREMIUM\n", "07/01/2025\n", "07/01/2026\n", "AGENCY\n", "MO\n", "$\n", "$\n", "$\n", "DIRECT\n", "APPLICANT INFORMATION\n", "NAME (First Named Insured) AND MAILING ADDRESS (including ZIP+4)\n", "GL CODE\n", "SIC\n", "NAICS\n", "FEIN OR SOC SEC #\n", "Merchants Landscape Services, Inc.\n", "0782\n", "561730\n", "95-4725606\n", "1190 Monterey Pass Road\n", "BUSINESS\n", "PHONE\n", "#: (*************\n", "Monterey Park, CA 91754\n", "WEBSITE ADDRESS\n", "https://www.merchantslandscape.com/\n", "CORPORATION\n", "JOINT VENTURE\n", "NOT FOR PROFIT ORG\n", "SUBCHAPTER \"S\" CORPORATION\n", "NO. OF MEMBERS\n", "INDIVIDUAL\n", "LLC\n", "AND MANAGERS:\n", "PARTNERSHIP\n", "TRUST\n", "Client\n", "NAME (Other Named Insured) AND MAILING ADDRESS (including ZIP+4)\n", "GL CODE\n", "SIC\n", "NAICS\n", "FEIN OR SOC SEC #\n", "Merchants Landscape Services, Inc.\n", "97047\n", "0782\n", "561730\n", "95-4725606\n", "1190 Monterey Pass Road\n", "Monterey Park, CA 91754\n", "BUSINESS PHONE #:\n", "************** 609\n", "WEBSITE ADDRESS\n", "CORPORATION\n", "JOINT VENTURE\n", "NOT FOR PROFIT ORG\n", "SUBCHAPTER \"S\" CORPORATION\n", "Commercial contractor landscaper, no r\n", "NO. OF MEMBERS\n", "INDIVIDUAL\n", "LLC\n", "AND MANAGERS:\n", "PARTNERSHIP\n", "TRUST\n", "NAME (Other Named Insured) AND MAILING ADDRESS (including ZIP+4)\n", "GL CODE\n", "SIC\n", "NAICS\n", "FEIN OR SOC SEC #\n", "Merchants Landscaping, Inc.\n", "1190 Monterey Pass Road\n", "Monterey Park, CA 91754\n", "BUSINESS PHONE #:\n", "WEBSITE ADDRESS\n", "CORPORATION\n", "JOINT VENTURE\n", "NOT FOR PROFIT ORG\n", "SUBCHAPTER \"S\" CORPORATION\n", "INDIVIDUAL\n", "LLC\n", "NO. OF MEMBERS\n", "PARTNERSHIP\n", "TRUST\n", "AND MANAGERS:\n", "ACORD 125 CA (2023/01)\n", "Page 1 of 4\n", "© 2022 ACORD CORPORATION. All rights reserved.\n", "The ACORD name and logo are registered marks of ACORD\n", "\n", "=== TABLE 1 ===\n", "COMPANY POLICY OR PROGRAM NAME,PROGRAM CODE\n", ",\n", "\n", "=== TABLE 2 ===\n", "UNDERWRITER,UNDERWRITER OFFICE\n", ",\n", "\n", "=== TABLE 3 ===\n", ",,QUOTE,,,,ISSUE,POLICY,,RENEW,\n", "STATUS OF TRANSACTION,,BOUND,(Give Date,,and/or,Attach,Copy):,,,\n", ",,CHANGE,,DATE,,,TIME,,,AM\n", ",,CANCEL,,,,,,,,PM\n", "\n", "=== TABLE 4 ===\n", ",INDICATE LINES OF BUSINESS,PREMIUM\n", ",BOILER & MACHINERY,$\n", ",BUSINESS AUTO,$\n", ",BUSINESS OWNERS,$\n", ",COMMERCIAL GENERAL LIABILITY,$\n", ",COMMERCIAL INLAND MARINE,$\n", ",COMMERCIAL PROPERTY,$\n", ",CRIME,$\n", "\n", "=== TABLE 5 ===\n", ",,PREMIUM\n", ",CYBER AND PRIVACY,$\n", ",FIDUCIARY LIABILITY,$\n", ",GARAGE AND DEALERS,$\n", ",LIQUOR LIABILITY,$\n", ",MOTOR CARRIER,$\n", ",TRUCKERS,$\n", ",UMBRELLA,$\n", "\n", "=== TABLE 6 ===\n", ",,PREMIUM\n", ",YACHT,$\n", ",,$\n", ",,$\n", ",,$\n", ",,$\n", ",,$\n", ",,$\n", "\n", "=== TABLE 7 ===\n", ",ACCOUNTS RECEIVABLE / VALUABLE PAPERS,,<PERSON><PERSON><PERSON> AND SIGN SECTION,,STATEMENT / SCHEDULE OF VALUES\n", ",ADDITIONAL INTEREST SCHEDULE,,HOTEL / MOTEL SUPPLEMENT,,STATE SUPPLEMENT (If applicable)\n", ",ADDITIONAL PREMISES INFORMATION SCHEDULE,,INSTALLATION / BUILDERS RISK SECTION,,VACANT BUILDING SUPPLEMENT\n", ",APARTMENT BUILDING SUPPLEMENT,,INTERNATIONAL LIABILITY EXPOSURE SUPPLEMENT,,VEH<PERSON><PERSON> SCHEDULE\n", ",CONDO ASSN BYLAWS (for D&O Coverage only),,INTERNATIONAL PROPERTY EXPOSURE SUPPLEMENT,,\n", ",CONTRACTORS SUPPLEMENT,,LOSS SUMMARY,,\n", ",COVERAGES SCHEDULE,,OPEN CARGO SECTION,,\n", ",DEALERS SECTION,,PREMIUM PAYMENT SUPPLEMENT,,\n", ",DRIVER INFORMATION SCHEDULE,,PROFESSIONAL LIABILITY SUPPLEMENT,,\n", ",ELECTRONIC DATA PROCESSING SECTION,,RESTAURANT / TAVERN SUPPLEMENT,,\n", "\n", "=== TABLE 8 ===\n", "PROPOSED EFF DATE,PROPOSED EXP DATE,B<PERSON><PERSON>ING,PLAN,,PAYMENT PLAN,METHOD OF PAYMENT,AUDIT,DEPOSIT,MINIMUM PREMIUM,POLICY PREMIUM\n", "07/01/2025,07/01/2026,DIRECT,,AGENCY,MO,,,$,$,$\n", "\n", "=== TABLE 9 ===\n", "GL CODE,SIC,NAICS,FEIN OR SOC SEC #\n", ",0782,561730,95-4725606\n", "\n", "=== TABLE 10 ===\n", ",CORPORATION,,JOINT VENTURE,,NOT FOR PROFIT ORG,,\"SUBCHAPTER \"\"S\"\" CORPORATION\",,\n", ",INDIVIDUAL,,NO. OF MEMBERS LLC AND MANAGERS:,,PARTNERSHIP,,TRUST,,\n", "\n", "=== TABLE 11 ===\n", "GL CODE,SIC,NAICS,FEIN OR SOC SEC #\n", "97047,0782,561730,95-4725606\n", "\n", "=== TABLE 12 ===\n", ",CORPORATION,,JOINT VENTURE,,NOT FOR PROFIT ORG,,\"SUBCHAPTER \"\"S\"\" CORPORATION\",,\"Commercial contractor landscaper, no r\"\n", ",INDIVIDUAL,,NO. OF MEMBERS LLC AND MANAGERS:,,PARTNERSHIP,,TRUST,,\n", "\n", "=== TABLE 13 ===\n", "GL CODE,SIC,NAICS,FEIN OR SOC SEC #\n", ",,,\n", "\n", "=== TABLE 14 ===\n", ",CORPORATION,,JOINT VENTURE,,NOT FOR PROFIT ORG,,\"SUBCHAPTER \"\"S\"\" CORPORATION\",,\n", ",INDIVIDUAL,,LLC NO. OF MEMBERS AND MANAGERS:,,PARTNERSHIP,,TRUST,,\n", "\n"]}], "source": ["print(\"=== STRUCTURED EXTRACTED TEXT ===\")\n", "print(structured_text)"]}, {"cell_type": "markdown", "id": "c93e96db", "metadata": {}, "source": ["# Bedrock - Converse - Simple"]}, {"cell_type": "code", "execution_count": 1, "id": "196d2a6b", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'structured_text' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 15\u001b[0m\n\u001b[1;32m     11\u001b[0m \u001b[38;5;66;03m# Define the model and message\u001b[39;00m\n\u001b[1;32m     12\u001b[0m model_id \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mamazon.nova-micro-v1:0\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m     13\u001b[0m messages \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m     14\u001b[0m     {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrole\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: [{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtext\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mYour task is to convert given data into json format\u001b[39m\u001b[38;5;124m\"\u001b[39m}]},\n\u001b[0;32m---> 15\u001b[0m     {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrole\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: [{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtext\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[43mstructured_text\u001b[49m}]}\n\u001b[1;32m     16\u001b[0m ]\n\u001b[1;32m     18\u001b[0m \u001b[38;5;66;03m# Make the API call\u001b[39;00m\n\u001b[1;32m     19\u001b[0m response \u001b[38;5;241m=\u001b[39m client\u001b[38;5;241m.\u001b[39mconverse(\n\u001b[1;32m     20\u001b[0m     modelId\u001b[38;5;241m=\u001b[39mmodel_id,\n\u001b[1;32m     21\u001b[0m     messages\u001b[38;5;241m=\u001b[39mmessages,\n\u001b[1;32m     22\u001b[0m )\n", "\u001b[0;31mNameError\u001b[0m: name 'structured_text' is not defined"]}], "source": ["import boto3\n", "\n", "# Create the Bedrock client using your SSO profile\n", "session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "\n", "client = session.client(\n", "    service_name=\"bedrock-runtime\",\n", "    region_name=\"us-east-1\"  # use the region where Bedrock is enabled\n", ")\n", "\n", "# Define the model and message\n", "model_id = \"amazon.nova-micro-v1:0\"\n", "messages = [\n", "    {\"role\": \"user\", \"content\": [{\"text\": \"Your task is to convert given data into json format\"}]},\n", "    {\"role\": \"user\", \"content\": [{\"text\": structured_text}]}\n", "]\n", "\n", "# Make the API call\n", "response = client.converse(\n", "    modelId=model_id,\n", "    messages=messages,\n", ")\n", "\n", "# Print the response\n", "print(response['output']['message']['content'][0]['text'])\n"]}, {"cell_type": "code", "execution_count": 22, "id": "38ce1cdf", "metadata": {}, "outputs": [{"ename": "ParamValidationError", "evalue": "Parameter validation failed:\nUnknown parameter in input: \"response_model\", must be one of: modelId, messages, system, inferenceConfig, toolConfig, guardrailConfig, additionalModelRequestFields, promptVariables, additionalModelResponseFieldPaths, requestMetadata, performanceConfig", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mParamValidationError\u001b[0m                      <PERSON>back (most recent call last)", "Cell \u001b[0;32mIn[22], line 26\u001b[0m\n\u001b[1;32m     20\u001b[0m messages \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m     21\u001b[0m     {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrole\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: [{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtext\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mYour task is to convert given data into json format\u001b[39m\u001b[38;5;124m\"\u001b[39m}]},\n\u001b[1;32m     22\u001b[0m     {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrole\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: [{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtext\u001b[39m\u001b[38;5;124m\"\u001b[39m: structured_text}]}\n\u001b[1;32m     23\u001b[0m ]\n\u001b[1;32m     25\u001b[0m \u001b[38;5;66;03m# Make the API call\u001b[39;00m\n\u001b[0;32m---> 26\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[43mclient\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconverse\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     27\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmodelId\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmodel_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     28\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmessages\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     29\u001b[0m \u001b[43m    \u001b[49m\u001b[43mresponse_model\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mUser\u001b[49m\n\u001b[1;32m     30\u001b[0m \u001b[43m)\u001b[49m\n\u001b[1;32m     32\u001b[0m \u001b[38;5;66;03m# Print the response\u001b[39;00m\n\u001b[1;32m     33\u001b[0m \u001b[38;5;28mprint\u001b[39m(response[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124moutput\u001b[39m\u001b[38;5;124m'\u001b[39m][\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmessage\u001b[39m\u001b[38;5;124m'\u001b[39m][\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m'\u001b[39m][\u001b[38;5;241m0\u001b[39m][\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtext\u001b[39m\u001b[38;5;124m'\u001b[39m])\n", "File \u001b[0;32m~/Documents/repositories/reference_code/.venv/lib/python3.10/site-packages/botocore/client.py:601\u001b[0m, in \u001b[0;36mClientCreator._create_api_method.<locals>._api_call\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m    597\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(\n\u001b[1;32m    598\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpy_operation_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m() only accepts keyword arguments.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    599\u001b[0m     )\n\u001b[1;32m    600\u001b[0m \u001b[38;5;66;03m# The \"self\" in this scope is referring to the BaseClient.\u001b[39;00m\n\u001b[0;32m--> 601\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_api_call\u001b[49m\u001b[43m(\u001b[49m\u001b[43moperation_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/Documents/repositories/reference_code/.venv/lib/python3.10/site-packages/botocore/context.py:123\u001b[0m, in \u001b[0;36mwith_current_context.<locals>.decorator.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    121\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m hook:\n\u001b[1;32m    122\u001b[0m     hook()\n\u001b[0;32m--> 123\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/Documents/repositories/reference_code/.venv/lib/python3.10/site-packages/botocore/client.py:1031\u001b[0m, in \u001b[0;36mBaseClient._make_api_call\u001b[0;34m(self, operation_name, api_params)\u001b[0m\n\u001b[1;32m   1027\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m properties:\n\u001b[1;32m   1028\u001b[0m     \u001b[38;5;66;03m# Pass arbitrary endpoint info with the Request\u001b[39;00m\n\u001b[1;32m   1029\u001b[0m     \u001b[38;5;66;03m# for use during construction.\u001b[39;00m\n\u001b[1;32m   1030\u001b[0m     request_context[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mendpoint_properties\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m properties\n\u001b[0;32m-> 1031\u001b[0m request_dict \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_convert_to_request_dict\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1032\u001b[0m \u001b[43m    \u001b[49m\u001b[43mapi_params\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mapi_params\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1033\u001b[0m \u001b[43m    \u001b[49m\u001b[43moperation_model\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moperation_model\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1034\u001b[0m \u001b[43m    \u001b[49m\u001b[43mendpoint_url\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mendpoint_url\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1035\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcontext\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest_context\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1036\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43madditional_headers\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1037\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1038\u001b[0m resolve_checksum_context(request_dict, operation_model, api_params)\n\u001b[1;32m   1040\u001b[0m service_id \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_service_model\u001b[38;5;241m.\u001b[39mservice_id\u001b[38;5;241m.\u001b[39mhyphenize()\n", "File \u001b[0;32m~/Documents/repositories/reference_code/.venv/lib/python3.10/site-packages/botocore/client.py:1098\u001b[0m, in \u001b[0;36mBaseClient._convert_to_request_dict\u001b[0;34m(self, api_params, operation_model, endpoint_url, context, headers, set_user_agent_header)\u001b[0m\n\u001b[1;32m   1089\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_convert_to_request_dict\u001b[39m(\n\u001b[1;32m   1090\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   1091\u001b[0m     api_params,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1096\u001b[0m     set_user_agent_header\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m,\n\u001b[1;32m   1097\u001b[0m ):\n\u001b[0;32m-> 1098\u001b[0m     request_dict \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_serializer\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mserialize_to_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1099\u001b[0m \u001b[43m        \u001b[49m\u001b[43mapi_params\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moperation_model\u001b[49m\n\u001b[1;32m   1100\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1101\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_client_config\u001b[38;5;241m.\u001b[39minject_host_prefix:\n\u001b[1;32m   1102\u001b[0m         request_dict\u001b[38;5;241m.\u001b[39mpop(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhost_prefix\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m)\n", "File \u001b[0;32m~/Documents/repositories/reference_code/.venv/lib/python3.10/site-packages/botocore/validate.py:381\u001b[0m, in \u001b[0;36mParamValidationDecorator.serialize_to_request\u001b[0;34m(self, parameters, operation_model)\u001b[0m\n\u001b[1;32m    377\u001b[0m     report \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_param_validator\u001b[38;5;241m.\u001b[39mvalidate(\n\u001b[1;32m    378\u001b[0m         parameters, operation_model\u001b[38;5;241m.\u001b[39minput_shape\n\u001b[1;32m    379\u001b[0m     )\n\u001b[1;32m    380\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m report\u001b[38;5;241m.\u001b[39mhas_errors():\n\u001b[0;32m--> 381\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m ParamValidationError(report\u001b[38;5;241m=\u001b[39mreport\u001b[38;5;241m.\u001b[39mgenerate_report())\n\u001b[1;32m    382\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_serializer\u001b[38;5;241m.\u001b[39mserialize_to_request(\n\u001b[1;32m    383\u001b[0m     parameters, operation_model\n\u001b[1;32m    384\u001b[0m )\n", "\u001b[0;31mParamValidationError\u001b[0m: Parameter validation failed:\nUnknown parameter in input: \"response_model\", must be one of: modelId, messages, system, inferenceConfig, toolConfig, guardrailConfig, additionalModelRequestFields, promptVariables, additionalModelResponseFieldPaths, requestMetadata, performanceConfig"]}], "source": ["import boto3\n", "import instructor\n", "from pydantic import BaseModel\n", "\n", "# Create the Bedrock client using your SSO profile\n", "session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "\n", "client = session.client(\n", "    service_name=\"bedrock-runtime\",\n", "    region_name=\"us-east-1\"  # use the region where Bedrock is enabled\n", ")\n", "\n", "class User(BaseModel):\n", "    name: str\n", "    age: int\n", "    addresses: list\n", "\n", "# Define the model and message\n", "model_id = \"amazon.nova-micro-v1:0\"\n", "messages = [\n", "    {\"role\": \"user\", \"content\": [{\"text\": \"Your task is to convert given data into json format\"}]},\n", "    {\"role\": \"user\", \"content\": [{\"text\": structured_text}]}\n", "]\n", "\n", "# Make the API call\n", "response = client.converse(\n", "    modelId=model_id,\n", "    messages=messages,\n", "    response_model=User\n", ")\n", "\n", "# Print the response\n", "print(response['output']['message']['content'][0]['text'])\n"]}, {"cell_type": "markdown", "id": "e19e470d", "metadata": {}, "source": ["# Bedrock - ChatCompletion - Instructor - Stuctured output"]}, {"cell_type": "code", "execution_count": 69, "id": "783f2522", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["details={'applicantInformation': {'date': '05/20/2025', 'producer': {'name': 'IMA, Inc. - Pasadena', 'address': '3475 E. Foothill Boulevard Suite 100 Pasadena, CA 91107', 'phone': '**************', 'fax': '**************'}, 'carrier': None, 'naicCode': 'N/A', 'marketingCompany': None, 'policyOrProgramName': None, 'programCode': None, 'policyNumber': 'TBD', 'underwriter': None, 'underwriterOffice': None, 'quoteStatusOfTransaction': None, 'boundDate': None, 'boundAttachedCopy': None, 'changeDate': None, 'changeTime': None, 'changeTimePeriod': None, 'cancelTransactionTime': None, 'cancelTransactionTimePeriod': None, 'agencyCustomerId': 'MERCLAN-C2', 'linesOfBusiness': {'boilerAndMachinery': None, 'businessAuto': None, 'businessOwners': None, 'commercialGeneralLiability': None, 'commercialInlandMarine': None, 'commercialProperty': None, 'crime': None}, 'cyberAndPrivacy': None, 'fiduciaryLiability': None, 'garageAndDealers': None, 'liquorLiability': None, 'motorCarrier': None, 'truckers': None, 'umbrella': None, 'yacht': None, 'attachments': ['ACCOUNTS RECEIVABLE / VALUABLE PAPERS', 'ADDITIONAL INTEREST SCHEDULE', 'ADDITIONAL PREMISES INFORMATION SCHEDULE', 'APARTMENT BUILDING SUPPLEMENT', 'CONDO ASSN BYLAWS (for D&O Coverage only)', 'CONTRACTORS SUPPLEMENT', 'COVERAGES SCHEDULE', 'DEALERS SECTION', 'DRIVER INFORMATION SCHEDULE', 'ELECTRONIC DATA PROCESSING SECTION', 'GLASS AND SIGN SECTION', 'HOTEL / MOTEL SUPPLEMENT', 'INSTALLATION / BUILDERS RISK SECTION', 'INTERNATIONAL LIABILITY EXPOSURE SUPPLEMENT', 'INTERNATIONAL PROPERTY EXPOSURE SUPPLEMENT', 'LOSS SUMMARY', 'OPEN CARGO SECTION', 'PREMIUM PAYMENT SUPPLEMENT', 'PROFESSIONAL LIABILITY SUPPLEMENT', 'RESTAURANT / TAVERN SUPPLEMENT', 'STATE SUPPLEMENT (If applicable)', 'STATEMENT / SCHEDULE OF VALUES', 'VACANT BUILDING SUPPLEMENT', 'VEHICLE SCHEDULE'], 'proposedEffectiveDate': '07/01/2025', 'proposedExpirationDate': '07/01/2026', 'billingPlan': 'DIRECT', 'paymentPlan': 'AGENCY', 'paymentMethod': 'MO', 'audit': None, 'deposit': None, 'minimumPremium': None, 'policyPremium': None}, 'insuredInformation': [{'name': 'Merchants Landscape Services, Inc.', 'mailingAddress': '1190 Monterey Pass Road Monterey Park, CA 91754', 'glCode': '0782', 'sic': '561730', 'naics': '561730', 'feinOrSsn': '95-4725606', 'businessPhone': '(*************', 'website': 'https://www.merchantslandscape.com/', 'entityType': 'CORPORATION', 'jointVenture': None, 'nonProfitOrg': None, 'subchapterSCorporation': None, 'individual': None, 'llc': None, 'numberOfMembersAndManagers': None, 'partnership': None, 'trust': None}, {'name': 'Merchants Landscape Services, Inc.', 'mailingAddress': '1190 Monterey Pass Road Monterey Park, CA 91754', 'glCode': '97047', 'sic': '0782', 'naics': '561730', 'feinOrSsn': '95-4725606', 'businessPhone': '************** 609', 'website': None, 'entityType': 'CORPORATION', 'jointVenture': None, 'nonProfitOrg': None, 'subchapterSCorporation': 'Commercial contractor landscaper, no r', 'individual': None, 'llc': None, 'numberOfMembersAndManagers': None, 'partnership': None, 'trust': None}, {'name': 'Merchants Landscaping, Inc.', 'mailingAddress': '1190 Monterey Pass Road Monterey Park, CA 91754', 'glCode': None, 'sic': None, 'naics': None, 'feinOrSsn': None, 'businessPhone': None, 'website': None, 'entityType': None, 'jointVenture': None, 'nonProfitOrg': None, 'subchapterSCorporation': None, 'individual': None, 'llc': None, 'numberOfMembersAndManagers': None, 'partnership': None, 'trust': None}]}\n"]}], "source": ["import boto3\n", "import instructor\n", "from pydantic import BaseModel\n", "import base64\n", "\n", "# Create the Bedrock client using your SSO profile\n", "session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "\n", "# Initialize the Bedrock client\n", "bedrock_client = session.client('bedrock-runtime')\n", "\n", "# Enable instructor patches for Bedrock client\n", "client = instructor.from_bedrock(bedrock_client)\n", "\n", "image_path = \"../document_ai/page1_doc1_2526 TABBED APP 4 (2) (1).jpg\"\n", "\n", "class User(BaseModel):\n", "    details: dict\n", "\n", "# Open the image file, read its binary content, and encode it in base64\n", "with open(image_path, \"rb\") as image_file:\n", "    encoded_string = base64.b64encode(image_file.read()).decode('utf-8')\n", "\n", "# Create structured output with nested objects\n", "user = client.chat.completions.create(\n", "        modelId=\"anthropic.claude-3-sonnet-********-v1:0\",\n", "        messages=[\n", "            {\"role\": \"user\", \"content\": [{\"text\": \"Your task is to convert given data into json format\"}]},\n", "            {\"role\": \"user\", \"content\": [{\"text\": structured_text}]},\n", "        ],\n", "        response_model=User,\n", "    )\n", "\n", "print(user)"]}, {"cell_type": "code", "execution_count": 72, "id": "********", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'applicantInformation': {'agencyCustomerId': 'MERCLAN-C2',\n", "                          'attachments': ['ACCOUNTS RECEIVABLE / VALUABLE '\n", "                                          'PAPERS',\n", "                                          'ADDITIONAL INTEREST SCHEDULE',\n", "                                          'ADDITIONAL PREMISES INFORMATION '\n", "                                          'SCHEDULE',\n", "                                          'APARTMENT BUILDING SUPPLEMENT',\n", "                                          'CONDO ASSN BYLAWS (for D&O Coverage '\n", "                                          'only)',\n", "                                          'CONTRACTORS SUPPLEMENT',\n", "                                          'COVERAGES SCHEDULE',\n", "                                          'DEALERS SECTION',\n", "                                          'DRIVER INFORMATION SCHEDULE',\n", "                                          'ELECTRONIC DATA PROCESSING SECTION',\n", "                                          'GLASS AND SIGN SECTION',\n", "                                          'HOTEL / MOTEL SUPPLEMENT',\n", "                                          'INSTALLATION / BUILDERS RISK '\n", "                                          'SECTION',\n", "                                          'INTERNATIONAL LIABILITY EXPOSURE '\n", "                                          'SUPPLEMENT',\n", "                                          'INTERNATIONAL PROPERTY EXPOSURE '\n", "                                          'SUPPLEMENT',\n", "                                          'LOSS SUMMARY',\n", "                                          'OPEN CARGO SECTION',\n", "                                          'PREMIUM PAYMENT SUPPLEMENT',\n", "                                          'PROFESSIONAL LIABILITY SUPPLEMENT',\n", "                                          'RESTAURANT / TAVERN SUPPLEMENT',\n", "                                          'STATE SUPPLEMENT (If applicable)',\n", "                                          'STATEMENT / SCHEDULE OF VALUES',\n", "                                          'VACANT BUILDING SUPPLEMENT',\n", "                                          'VEHICLE SCHEDULE'],\n", "                          'audit': None,\n", "                          'billingPlan': 'DIRECT',\n", "                          'boundAttachedCopy': None,\n", "                          'boundDate': None,\n", "                          'cancelTransactionTime': None,\n", "                          'cancelTransactionTimePeriod': None,\n", "                          'carrier': None,\n", "                          'changeDate': None,\n", "                          'changeTime': None,\n", "                          'changeTimePeriod': None,\n", "                          'cyberAndPrivacy': None,\n", "                          'date': '05/20/2025',\n", "                          'deposit': None,\n", "                          'fiduciaryLiability': None,\n", "                          'garageAndDealers': None,\n", "                          'linesOfBusiness': {'boilerAndMachinery': None,\n", "                                              'businessAuto': None,\n", "                                              'businessOwners': None,\n", "                                              'commercialGeneralLiability': None,\n", "                                              'commercialInlandMarine': None,\n", "                                              'commercialProperty': None,\n", "                                              'crime': None},\n", "                          'liquorLiability': None,\n", "                          'marketingCompany': None,\n", "                          'minimumPremium': None,\n", "                          'motorCarrier': None,\n", "                          'naicCode': 'N/A',\n", "                          'paymentMethod': 'MO',\n", "                          'paymentPlan': 'AGENCY',\n", "                          'policyNumber': 'TBD',\n", "                          'policyOrProgramName': None,\n", "                          'policyPremium': None,\n", "                          'producer': {'address': '3475 E. Foothill Boulevard '\n", "                                                  'Suite 100 Pasadena, CA '\n", "                                                  '91107',\n", "                                       'fax': '**************',\n", "                                       'name': 'IMA, Inc. - Pasadena',\n", "                                       'phone': '**************'},\n", "                          'programCode': None,\n", "                          'proposedEffectiveDate': '07/01/2025',\n", "                          'proposedExpirationDate': '07/01/2026',\n", "                          'quoteStatusOfTransaction': None,\n", "                          'truckers': None,\n", "                          'umbrella': None,\n", "                          'underwriter': None,\n", "                          'underwriterOffice': None,\n", "                          'yacht': None},\n", " 'insuredInformation': [{'businessPhone': '(*************',\n", "                         'entityType': 'CORPORATION',\n", "                         'feinOrSsn': '95-4725606',\n", "                         'glCode': '0782',\n", "                         'individual': None,\n", "                         'jointVenture': None,\n", "                         'llc': None,\n", "                         'mailingAddress': '1190 Monterey Pass Road Monterey '\n", "                                           'Park, CA 91754',\n", "                         'naics': '561730',\n", "                         'name': 'Merchants Landscape Services, Inc.',\n", "                         'nonProfitOrg': None,\n", "                         'numberOfMembersAndManagers': None,\n", "                         'partnership': None,\n", "                         'sic': '561730',\n", "                         'subchapterSCorporation': None,\n", "                         'trust': None,\n", "                         'website': 'https://www.merchantslandscape.com/'},\n", "                        {'businessPhone': '************** 609',\n", "                         'entityType': 'CORPORATION',\n", "                         'feinOrSsn': '95-4725606',\n", "                         'glCode': '97047',\n", "                         'individual': None,\n", "                         'jointVenture': None,\n", "                         'llc': None,\n", "                         'mailingAddress': '1190 Monterey Pass Road Monterey '\n", "                                           'Park, CA 91754',\n", "                         'naics': '561730',\n", "                         'name': 'Merchants Landscape Services, Inc.',\n", "                         'nonProfitOrg': None,\n", "                         'numberOfMembersAndManagers': None,\n", "                         'partnership': None,\n", "                         'sic': '0782',\n", "                         'subchapterSCorporation': 'Commercial contractor '\n", "                                                   'landscaper, no r',\n", "                         'trust': None,\n", "                         'website': None},\n", "                        {'businessPhone': None,\n", "                         'entityType': None,\n", "                         'feinOrSsn': None,\n", "                         'glCode': None,\n", "                         'individual': None,\n", "                         'jointVenture': None,\n", "                         'llc': None,\n", "                         'mailingAddress': '1190 Monterey Pass Road Monterey '\n", "                                           'Park, CA 91754',\n", "                         'naics': None,\n", "                         'name': 'Merchants Landscaping, Inc.',\n", "                         'nonProfitOrg': None,\n", "                         'numberOfMembersAndManagers': None,\n", "                         'partnership': None,\n", "                         'sic': None,\n", "                         'subchapterSCorporation': None,\n", "                         'trust': None,\n", "                         'website': None}]}\n"]}], "source": ["pprint(user.details)"]}, {"cell_type": "code", "execution_count": 47, "id": "b4c86200", "metadata": {}, "outputs": [{"ename": "ValidationException", "evalue": "An error occurred (ValidationException) when calling the InvokeModel operation: Malformed input request: #: subject must not be valid against schema {\"required\":[\"messages\"]}#: extraneous key [tool_config] is not permitted#/tools/0: required key [type] not found#/tools/0: required key [name] not found#/tools/0: extraneous key [toolSpec] is not permitted#/tools/0: required key [type] not found#/tools/0: required key [name] not found#/tools/0: required key [display_height_px] not found#/tools/0: required key [display_width_px] not found#/tools/0: extraneous key [toolSpec] is not permitted#/tools/0: required key [type] not found#/tools/0: required key [name] not found#/tools/0: extraneous key [toolSpec] is not permitted#/tools/0: required key [name] not found#/tools/0: required key [input_schema] not found#/tools/0: extraneous key [toolSpec] is not permitted, please reformat your input and try again.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mValidationException\u001b[0m                       Traceback (most recent call last)", "Cell \u001b[0;32mIn[47], line 69\u001b[0m\n\u001b[1;32m     35\u001b[0m request_body \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m     36\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124manthropic_version\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mbedrock-2023-05-31\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     37\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmax_tokens\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;241m2048\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     65\u001b[0m     }\n\u001b[1;32m     66\u001b[0m }\n\u001b[1;32m     68\u001b[0m \u001b[38;5;66;03m# 5. Invoke the model\u001b[39;00m\n\u001b[0;32m---> 69\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[43mbedrock_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minvoke_model\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     70\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmodelId\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43manthropic.claude-3-5-sonnet-20240620-v1:0\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m     71\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mjson\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdumps\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest_body\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     72\u001b[0m \u001b[43m)\u001b[49m\n\u001b[1;32m     74\u001b[0m \u001b[38;5;66;03m# 6. Parse the response to get the structured data\u001b[39;00m\n\u001b[1;32m     75\u001b[0m response_body \u001b[38;5;241m=\u001b[39m json\u001b[38;5;241m.\u001b[39mloads(response\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mbody\u001b[39m\u001b[38;5;124m\"\u001b[39m)\u001b[38;5;241m.\u001b[39mread())\n", "File \u001b[0;32m~/Documents/repositories/reference_code/.venv/lib/python3.10/site-packages/botocore/client.py:601\u001b[0m, in \u001b[0;36mClientCreator._create_api_method.<locals>._api_call\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m    597\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(\n\u001b[1;32m    598\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpy_operation_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m() only accepts keyword arguments.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    599\u001b[0m     )\n\u001b[1;32m    600\u001b[0m \u001b[38;5;66;03m# The \"self\" in this scope is referring to the BaseClient.\u001b[39;00m\n\u001b[0;32m--> 601\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_api_call\u001b[49m\u001b[43m(\u001b[49m\u001b[43moperation_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/Documents/repositories/reference_code/.venv/lib/python3.10/site-packages/botocore/context.py:123\u001b[0m, in \u001b[0;36mwith_current_context.<locals>.decorator.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    121\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m hook:\n\u001b[1;32m    122\u001b[0m     hook()\n\u001b[0;32m--> 123\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/Documents/repositories/reference_code/.venv/lib/python3.10/site-packages/botocore/client.py:1074\u001b[0m, in \u001b[0;36mBaseClient._make_api_call\u001b[0;34m(self, operation_name, api_params)\u001b[0m\n\u001b[1;32m   1070\u001b[0m     error_code \u001b[38;5;241m=\u001b[39m error_info\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mQueryErrorCode\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m error_info\u001b[38;5;241m.\u001b[39mget(\n\u001b[1;32m   1071\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCode\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1072\u001b[0m     )\n\u001b[1;32m   1073\u001b[0m     error_class \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mexceptions\u001b[38;5;241m.\u001b[39mfrom_code(error_code)\n\u001b[0;32m-> 1074\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m error_class(parsed_response, operation_name)\n\u001b[1;32m   1075\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1076\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m parsed_response\n", "\u001b[0;31mValidationException\u001b[0m: An error occurred (ValidationException) when calling the InvokeModel operation: Malformed input request: #: subject must not be valid against schema {\"required\":[\"messages\"]}#: extraneous key [tool_config] is not permitted#/tools/0: required key [type] not found#/tools/0: required key [name] not found#/tools/0: extraneous key [toolSpec] is not permitted#/tools/0: required key [type] not found#/tools/0: required key [name] not found#/tools/0: required key [display_height_px] not found#/tools/0: required key [display_width_px] not found#/tools/0: extraneous key [toolSpec] is not permitted#/tools/0: required key [type] not found#/tools/0: required key [name] not found#/tools/0: extraneous key [toolSpec] is not permitted#/tools/0: required key [name] not found#/tools/0: required key [input_schema] not found#/tools/0: extraneous key [toolSpec] is not permitted, please reformat your input and try again."]}], "source": ["import boto3\n", "import base64\n", "import json\n", "from pydantic import BaseModel, Field\n", "\n", "# 1. Define your desired JSON structure using Pydantic\n", "class DocumentDetails(BaseModel):\n", "    account_number: str = Field(..., description=\"The account number from the document.\")\n", "    applicant_name: str = Field(..., description=\"The full name of the primary applicant.\")\n", "    property_address: str = Field(..., description=\"The full property address including street, city, and zip code.\")\n", "    loan_amount: float = Field(..., description=\"The requested loan amount.\")\n", "\n", "# --- Standard Bedrock and Image Setup ---\n", "session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "bedrock_client = session.client('bedrock-runtime')\n", "image_path = \"../document_ai/page1_doc1_2526 TABBED APP 4 (2) (1).jpg\"\n", "\n", "# 2. Open and encode the image\n", "with open(image_path, \"rb\") as image_file:\n", "    encoded_string = base64.b64encode(image_file.read()).decode('utf-8')\n", "\n", "# 3. Define the tool for the model to use\n", "# This uses the schema from your Pydantic model\n", "tool_definition = {\n", "    \"toolSpec\": {\n", "        \"name\": \"extract_document_details\",\n", "        \"description\": \"Extracts the required details from the document image.\",\n", "        \"inputSchema\": {\n", "            \"json\": DocumentDetails.model_json_schema()\n", "        }\n", "    }\n", "}\n", "\n", "# 4. Construct the API request body\n", "request_body = {\n", "    \"anthropic_version\": \"bedrock-2023-05-31\",\n", "    \"max_tokens\": 2048,\n", "    \"messages\": [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": [\n", "                {\n", "                    \"type\": \"image\",\n", "                    \"source\": {\n", "                        \"type\": \"base64\",\n", "                        \"media_type\": \"image/jpeg\",\n", "                        \"data\": encoded_string,\n", "                    },\n", "                },\n", "                {\n", "                    \"type\": \"text\",\n", "                    \"content\": \"Extract the information from the document using the available tool.\",\n", "                },\n", "            ],\n", "        }\n", "    ],\n", "    # --- Add the tool definition and force the model to use it ---\n", "    \"tools\": [tool_definition],\n", "    \"tool_config\": {\n", "        \"toolChoice\": {\n", "            \"tool\": {\n", "                \"name\": \"extract_document_details\"\n", "            }\n", "        }\n", "    }\n", "}\n", "\n", "# 5. Invoke the model\n", "response = bedrock_client.invoke_model(\n", "    modelId=\"anthropic.claude-3-5-sonnet-20240620-v1:0\",\n", "    body=json.dumps(request_body),\n", ")\n", "\n", "# 6. Parse the response to get the structured data\n", "response_body = json.loads(response.get(\"body\").read())\n", "\n", "# Find the tool_use block in the response content\n", "tool_use_block = next(\n", "    (content for content in response_body[\"content\"] if \"toolUse\" in content), None\n", ")\n", "\n", "if tool_use_block:\n", "    tool_input = tool_use_block[\"toolUse\"][\"input\"]\n", "    \n", "    # The 'tool_input' is your final, schema-compliant JSON object\n", "    print(\"--- Strict JSON Output ---\")\n", "    print(json.dumps(tool_input, indent=2))\n", "    \n", "    # You can now load it directly into your Pydantic model for further use\n", "    validated_data = DocumentDetails(**tool_input)\n", "    print(\"\\n--- Pydantic Validated Object ---\")\n", "    print(validated_data)\n", "else:\n", "    print(\"Error: The model did not return the expected tool use block.\")"]}, {"cell_type": "code", "execution_count": null, "id": "2e2c5dd8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ee482f63", "metadata": {}, "outputs": [{"data": {"text/html": ["<script>Jupyter.notebook.kernel.restart()</script>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.core.display import HTML\n", "HTML(\"<script>Jupyter.notebook.kernel.restart()</script>\")"]}, {"cell_type": "markdown", "id": "19a31c50", "metadata": {}, "source": ["# Using raw API call"]}, {"cell_type": "code", "execution_count": null, "id": "f79034af", "metadata": {}, "outputs": [], "source": ["import boto3\n", "import json\n", "import base64\n", "\n", "session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "\n", "client = session.client(\n", "    service_name=\"textract\", \n", "    region_name=\"us-east-1\"  # use the region where Bedrock is enabled\n", ")"]}, {"cell_type": "code", "execution_count": 53, "id": "d4c005ae", "metadata": {}, "outputs": [], "source": ["def generate_message(bedrock_runtime, model_id, messages, max_tokens,top_p,temp):\n", "\n", "    body=json.dumps(\n", "        {\n", "            \"anthropic_version\": \"bedrock-2023-05-31\",\n", "            \"max_tokens\": max_tokens,\n", "            \"messages\": messages,\n", "            \"temperature\": temp,\n", "            \"top_p\": top_p\n", "        }  \n", "    )  \n", "    \n", "    response = bedrock_runtime.invoke_model(body=body, modelId=model_id)\n", "    response_body = json.loads(response.get('body').read())\n", "\n", "    return response_body\n"]}, {"cell_type": "code", "execution_count": 55, "id": "********", "metadata": {}, "outputs": [], "source": ["# Read reference image from file and encode as base64 strings.\n", "with open('page1_doc1_2526 TABBED APP 4 (2) (1).jpg', \"rb\") as image_file:\n", "    content_image = base64.b64encode(image_file.read()).decode('utf8')"]}, {"cell_type": "code", "execution_count": 56, "id": "543171c4", "metadata": {}, "outputs": [], "source": ["message_mm=[\n", "\n", "    { \"role\": \"user\",\n", "      \"content\": [\n", "      {\"type\": \"image\",\"source\": { \"type\": \"base64\",\"media_type\":\"image/jpeg\",\"data\": content_image}},\n", "      {\"type\": \"text\",\"text\": \"What is in this image?\"}\n", "      ]\n", "    }\n", "]"]}, {"cell_type": "code", "execution_count": 57, "id": "c70b5a95", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'id': 'msg_bdrk_019TZGs9T6UcSQifvVCiPJmP', 'type': 'message', 'role': 'assistant', 'model': 'claude-3-sonnet-********', 'content': [{'type': 'text', 'text': 'This image appears to be a California Commercial Insurance Application form from ACORD (Agency-Company Operations Research and Development). It is the Applicant Information Section of the form where details about the applicant business, such as the company name, address, business type (corporation, partnership, etc.), contact information, and other relevant details are to be provided. The form also has sections to specify the lines of business/coverage types desired, billing plan, proposed policy dates, and other supplemental sections that may need to be completed based on the specific insurance needs.'}], 'stop_reason': 'end_turn', 'stop_sequence': None, 'usage': {'input_tokens': 1513, 'output_tokens': 116}}"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["generate_message(bedrock_client, model_id = \"anthropic.claude-3-sonnet-********-v1:0\",messages=message_mm,max_tokens=512,temp=0.5,top_p=0.9)\n"]}, {"cell_type": "code", "execution_count": 59, "id": "6eac6c61", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'content': [{'text': 'This image appears to be a California Commercial '\n", "                      'Insurance Application form from ACORD (Agency-Company '\n", "                      'Operations Research and Development). It is the '\n", "                      'Applicant Information Section of the form where details '\n", "                      'about the applicant business, such as the company name, '\n", "                      'address, business type (corporation, partnership, '\n", "                      'etc.), contact information, and other relevant details '\n", "                      'are to be provided. The form also has sections to '\n", "                      'specify the lines of business/coverage types desired, '\n", "                      'billing plan, proposed policy dates, and other '\n", "                      'supplemental sections that may need to be completed '\n", "                      'based on the specific insurance needs.',\n", "              'type': 'text'}],\n", " 'id': 'msg_bdrk_019TZGs9T6UcSQifvVCiPJmP',\n", " 'model': 'claude-3-sonnet-********',\n", " 'role': 'assistant',\n", " 'stop_reason': 'end_turn',\n", " 'stop_sequence': None,\n", " 'type': 'message',\n", " 'usage': {'input_tokens': 1513, 'output_tokens': 116}}\n"]}], "source": ["pprint({'id': 'msg_bdrk_019TZGs9T6UcSQifvVCiPJmP', 'type': 'message', 'role': 'assistant', 'model': 'claude-3-sonnet-********', 'content': [{'type': 'text', 'text': 'This image appears to be a California Commercial Insurance Application form from ACORD (Agency-Company Operations Research and Development). It is the Applicant Information Section of the form where details about the applicant business, such as the company name, address, business type (corporation, partnership, etc.), contact information, and other relevant details are to be provided. The form also has sections to specify the lines of business/coverage types desired, billing plan, proposed policy dates, and other supplemental sections that may need to be completed based on the specific insurance needs.'}], 'stop_reason': 'end_turn', 'stop_sequence': None, 'usage': {'input_tokens': 1513, 'output_tokens': 116}})"]}, {"cell_type": "code", "execution_count": null, "id": "16186375", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "2aaab453", "metadata": {}, "source": ["# Bedrock - invoke model - jpg"]}, {"cell_type": "code", "execution_count": 1, "id": "a29a416e", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'pprint' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 45\u001b[0m\n\u001b[1;32m     39\u001b[0m response \u001b[38;5;241m=\u001b[39m bedrock_client\u001b[38;5;241m.\u001b[39minvoke_model(\n\u001b[1;32m     40\u001b[0m     body\u001b[38;5;241m=\u001b[39mbody, \n\u001b[1;32m     41\u001b[0m     modelId\u001b[38;5;241m=\u001b[39mmodel_id\n\u001b[1;32m     42\u001b[0m )\n\u001b[1;32m     44\u001b[0m response_body \u001b[38;5;241m=\u001b[39m json\u001b[38;5;241m.\u001b[39mloads(response\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbody\u001b[39m\u001b[38;5;124m'\u001b[39m)\u001b[38;5;241m.\u001b[39mread())\n\u001b[0;32m---> 45\u001b[0m \u001b[43mpprint\u001b[49m(response_body)\n", "\u001b[0;31mNameError\u001b[0m: name 'pprint' is not defined"]}], "source": ["import boto3\n", "from pprint import pprint\n", "import instructor\n", "from pydantic import BaseModel\n", "import base64\n", "import json\n", "\n", "# Create the Bedrock client using your SSO profile\n", "session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "\n", "# Initialize the Bedrock client\n", "bedrock_client = session.client('bedrock-runtime')\n", "\n", "# Read and encode the image\n", "with open('page1_doc1_2526 TABBED APP 4 (2) (1).jpg', \"rb\") as image_file:\n", "    content_image = base64.b64encode(image_file.read()).decode('utf8')\n", "\n", "# Define model_id\n", "model_id = \"anthropic.claude-3-sonnet-********-v1:0\"\n", "\n", "messages=[\n", "    { \"role\": \"user\",\n", "      \"content\": [\n", "        {\"type\": \"image\",\"source\": { \"type\": \"base64\",\"media_type\":\"image/jpeg\",\"data\": content_image}},\n", "        {\"type\": \"text\",\"text\": \"What is in this image?\"}\n", "      ]\n", "    }\n", "]\n", "\n", "body=json.dumps(\n", "    {\n", "        \"anthropic_version\": \"bedrock-2023-05-31\",\n", "        \"max_tokens\": 1024,\n", "        \"messages\": messages,\n", "        \"temperature\": 0.5,\n", "        \"top_p\": 0.9\n", "    }  \n", ")\n", "\n", "response = bedrock_client.invoke_model(\n", "    body=body, \n", "    modelId=model_id\n", ")\n", "\n", "response_body = json.loads(response.get('body').read())\n", "pprint(response_body)"]}, {"cell_type": "code", "execution_count": 41, "id": "4eb0cc0c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_11678/1599526493.py:13: PydanticDeprecatedSince20: The `schema` method is deprecated; use `model_json_schema` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/\n", "  schema = model.schema()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Structured Output:\n", "{\n", "    \"text\": \"The product was amazing, great quality and fast delivery, but the customer service could be improved.\"\n", "}\n"]}], "source": ["import boto3\n", "import json\n", "from pydantic import BaseModel\n", "\n", "# Define the Pydantic model for the structured output\n", "class ReviewAnalysis(BaseModel):\n", "    sentiment: str\n", "    score: float\n", "    key_points: list[str]\n", "\n", "# Utility function to convert Pydantic model to JSON schema for Bedrock tool\n", "def pydantic_to_tool_schema(model):\n", "    schema = model.schema()\n", "    return {\n", "        \"toolSpec\": {\n", "            \"name\": \"review_analysis\",\n", "            \"description\": \"Analyze a customer review and return sentiment, score, and key points.\",\n", "            \"inputSchema\": {\n", "                \"json\": schema\n", "            }\n", "        }\n", "    }\n", "\n", "session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "\n", "# Initialize Bedrock client\n", "bedrock_client = session.client(\"bedrock-runtime\", region_name=\"us-east-1\")\n", "\n", "# Define the tool and prompt\n", "tool_list = [pydantic_to_tool_schema(ReviewAnalysis)]\n", "system_prompts = [{\"text\": \"You are a review analysis tool. Use the provided tool to structure the output.\"}]\n", "user_message = {\n", "    \"role\": \"user\",\n", "    \"content\": [\n", "        {\n", "            \"text\": \"\"\"Analyze this review: 'The product was amazing, great quality and fast delivery, but the customer service could be improved.'\"\"\"\n", "        }\n", "    ]\n", "}\n", "\n", "# Make the Converse API call\n", "try:\n", "    response = bedrock_client.converse(\n", "        modelId=\"anthropic.claude-3-sonnet-********-v1:0\",\n", "        messages=[user_message],\n", "        system=system_prompts,\n", "        toolConfig={\"tools\": tool_list},\n", "        inferenceConfig={\n", "            \"maxTokens\": 512,\n", "            \"temperature\": 0.5,\n", "            \"topP\": 0.9\n", "        }\n", "    )\n", "\n", "    # Extract the structured output\n", "    output_message = response[\"output\"][\"message\"]\n", "    for content in output_message[\"content\"]:\n", "        if \"toolUse\" in content:\n", "            tool_result = content[\"toolUse\"][\"input\"]\n", "            print(\"Structured Output:\")\n", "            print(json.dumps(tool_result, indent=4))\n", "\n", "except Exception as e:\n", "    print(f\"Error: {e}\")"]}, {"cell_type": "code", "execution_count": 42, "id": "cbbf1568", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'role': 'assistant',\n", " 'content': [{'text': 'Here is the analysis from the review_analysis tool:'},\n", "  {'toolUse': {'toolUseId': 'tooluse_fUj5Hf1BRgaLJp1ytqdatA',\n", "    'name': 'review_analysis',\n", "    'input': {'text': 'The product was amazing, great quality and fast delivery, but the customer service could be improved.'}}}]}"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["output_message"]}, {"cell_type": "code", "execution_count": 7, "id": "7c4fd13c", "metadata": {}, "outputs": [], "source": ["from pprint import pprint"]}, {"cell_type": "code", "execution_count": 8, "id": "f8320add", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'toolSpec': {'description': 'Analyze a customer review and return sentiment, '\n", "                              'score, and key points.',\n", "               'inputSchema': {'json': {'properties': {'key_points': {'items': {'type': 'string'},\n", "                                                                      'title': 'Key '\n", "                                                                               'Points',\n", "                                                                      'type': 'array'},\n", "                                                       'score': {'title': 'Score',\n", "                                                                 'type': 'number'},\n", "                                                       'sentiment': {'title': 'Sentiment',\n", "                                                                     'type': 'string'}},\n", "                                        'required': ['sentiment',\n", "                                                     'score',\n", "                                                     'key_points'],\n", "                                        'title': 'ReviewAnalysis',\n", "                                        'type': 'object'}},\n", "               'name': 'review_analysis'}}]\n"]}], "source": ["pprint(tool_list)"]}, {"cell_type": "code", "execution_count": null, "id": "f39ef3e1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5b63440d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e9f1be09", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "f916938f", "metadata": {}, "source": ["# Bedrock - Invoke - jpg - Structured"]}, {"cell_type": "code", "execution_count": null, "id": "91723492", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d2a2b06b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b78894ff", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2e6a5e6d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "44e2c21d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3d2e8325", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6ffd54b3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c660f596", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "63fc7a96", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d2ef0022", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "1d42a849", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Full response:\n", "{'content': [{'id': 'toolu_bdrk_01DWRhocr8yN2miC5i7784Mn',\n", "              'input': {'confidence_score': 0.95,\n", "                        'document_type': 'Commercial Insurance Application '\n", "                                         'Form',\n", "                        'key_information': {'agency_customer_id': 'MERCLAN-C2',\n", "                                            'applicant_address': '1190 '\n", "                                                                 'Monterey '\n", "                                                                 'Pass Road '\n", "                                                                 'Monterey '\n", "                                                                 'Park, CA '\n", "                                                                 '91754',\n", "                                            'applicant_name': 'Merchants '\n", "                                                              'Landscape '\n", "                                                              'Services, Inc.',\n", "                                            'applicant_section': 'APPLICANT '\n", "                                                                 'INFORMATION '\n", "                                                                 'SECTION',\n", "                                            'billing_plan': 'AGENCY',\n", "                                            'business_phone': '(*************',\n", "                                            'company_type': 'CORPORATION',\n", "                                            'contact_fax': '**************',\n", "                                            'contact_phone': '**************',\n", "                                            'document_title': 'CALIFORNIA '\n", "                                                              'COMMERCIAL '\n", "                                                              'INSURANCE '\n", "                                                              'APPLICATION',\n", "                                            'fein_or_soc_sec': '95-4725606',\n", "                                            'gl_code': '0782',\n", "                                            'lines_of_business': ['COMMERCIAL '\n", "                                                                  'GENERAL '\n", "                                                                  'LIABILITY',\n", "                                                                  'UMBRELLA'],\n", "                                            'naics': '561730',\n", "                                            'payment_plan': 'MO',\n", "                                            'policy_effective_date': '07/01/2025',\n", "                                            'policy_expiration_date': '07/01/2026',\n", "                                            'producer': 'IMA, Inc. - Pasadena',\n", "                                            'producer_address': '3475 E. '\n", "                                                                'Foothill '\n", "                                                                'Boulevard '\n", "                                                                'Suite 100, '\n", "                                                                'Pasadena, CA '\n", "                                                                '91107',\n", "                                            'sic': '0782',\n", "                                            'website': 'https://www.merchantslandscape.com/'},\n", "                        'main_content': 'The document is a California '\n", "                                        'Commercial Insurance Application form '\n", "                                        'from ACORD. It includes sections for '\n", "                                        'applicant information, lines of '\n", "                                        'business, attachments, and policy '\n", "                                        'information. The form is partially '\n", "                                        'filled out with details of an '\n", "                                        'insurance application.'},\n", "              'name': 'analyze_document',\n", "              'type': 'tool_use'}],\n", " 'id': 'msg_bdrk_01WYz1AZUuLzXEzcYkFXGq4b',\n", " 'model': 'claude-3-5-sonnet-20240620',\n", " 'role': 'assistant',\n", " 'stop_reason': 'tool_use',\n", " 'stop_sequence': None,\n", " 'type': 'message',\n", " 'usage': {'input_tokens': 2013, 'output_tokens': 494}}\n", "\n", "=== Structured Analysis ===\n", "{\n", "  \"document_type\": \"Commercial Insurance Application Form\",\n", "  \"main_content\": \"The document is a California Commercial Insurance Application form from ACORD. It includes sections for applicant information, lines of business, attachments, and policy information. The form is partially filled out with details of an insurance application.\",\n", "  \"key_information\": {\n", "    \"document_title\": \"CALIFORNIA COMMERCIAL INSURANCE APPLICATION\",\n", "    \"applicant_section\": \"APPLICANT INFORMATION SECTION\",\n", "    \"producer\": \"IMA, Inc. - Pasadena\",\n", "    \"producer_address\": \"3475 E. Foothill Boulevard Suite 100, Pasadena, CA 91107\",\n", "    \"contact_phone\": \"**************\",\n", "    \"contact_fax\": \"**************\",\n", "    \"agency_customer_id\": \"MERCLAN-C2\",\n", "    \"policy_effective_date\": \"07/01/2025\",\n", "    \"policy_expiration_date\": \"07/01/2026\",\n", "    \"payment_plan\": \"MO\",\n", "    \"billing_plan\": \"AGENCY\",\n", "    \"applicant_name\": \"Merchants Landscape Services, Inc.\",\n", "    \"applicant_address\": \"1190 Monterey Pass Road Monterey Park, CA 91754\",\n", "    \"gl_code\": \"0782\",\n", "    \"sic\": \"0782\",\n", "    \"naics\": \"561730\",\n", "    \"fein_or_soc_sec\": \"95-4725606\",\n", "    \"business_phone\": \"(*************\",\n", "    \"website\": \"https://www.merchantslandscape.com/\",\n", "    \"lines_of_business\": [\n", "      \"COMMERCIAL GENERAL LIABILITY\",\n", "      \"UMBRELLA\"\n", "    ],\n", "    \"company_type\": \"CORPORATION\"\n", "  },\n", "  \"confidence_score\": 0.95\n", "}\n", "\n", "Document Type: Commercial Insurance Application Form\n", "Confidence: 0.95\n"]}], "source": ["import boto3\n", "from pydantic import BaseModel, Field\n", "import base64\n", "import json\n", "from pprint import pprint\n", "\n", "# Define your structured output schema\n", "class DocumentAnalysis(BaseModel):\n", "    document_type: str = Field(..., description=\"Type of document (e.g., form, invoice, letter)\")\n", "    main_content: str = Field(..., description=\"Main text content of the document\")\n", "    key_information: dict = Field(..., description=\"Key fields and values extracted from the document\")\n", "    confidence_score: float = Field(..., description=\"Confidence in the analysis (0-1)\")\n", "\n", "# Create the Bedrock client\n", "session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "bedrock_client = session.client('bedrock-runtime')\n", "\n", "# Read and encode the image\n", "with open('page1_doc1_2526 TABBED APP 4 (2) (1).jpg', \"rb\") as image_file:\n", "    content_image = base64.b64encode(image_file.read()).decode('utf8')\n", "\n", "# Define the tool using correct Bedrock format\n", "tool_definition = {\n", "    \"name\": \"analyze_document\",\n", "    \"description\": \"Analyzes document and extracts structured information\",\n", "    \"input_schema\": DocumentAnalysis.model_json_schema()\n", "}\n", "\n", "# Construct the request body with correct format\n", "request_body = {\n", "    \"anthropic_version\": \"bedrock-2023-05-31\",\n", "    \"max_tokens\": 2048,\n", "    \"messages\": [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": [\n", "                {\n", "                    \"type\": \"image\",\n", "                    \"source\": {\n", "                        \"type\": \"base64\",\n", "                        \"media_type\": \"image/jpeg\",\n", "                        \"data\": content_image,\n", "                    },\n", "                },\n", "                {\n", "                    \"type\": \"text\",\n", "                    \"text\": \"Analyze this document and extract key information using the available tool.\",\n", "                },\n", "            ],\n", "        }\n", "    ],\n", "    \"tools\": [tool_definition],\n", "    \"tool_choice\": {\"type\": \"tool\", \"name\": \"analyze_document\"}\n", "}\n", "\n", "try:\n", "    # Invoke the model\n", "    response = bedrock_client.invoke_model(\n", "        modelId=\"anthropic.claude-3-5-sonnet-20240620-v1:0\",\n", "        body=json.dumps(request_body),\n", "    )\n", "\n", "    # Parse the response\n", "    response_body = json.loads(response.get(\"body\").read())\n", "    \n", "    print(\"Full response:\")\n", "    pprint(response_body)\n", "    \n", "    # Extract structured data from tool use\n", "    tool_use_block = next(\n", "        (content for content in response_body[\"content\"] if content.get(\"type\") == \"tool_use\"), None\n", "    )\n", "\n", "    if tool_use_block:\n", "        structured_data = tool_use_block[\"input\"]\n", "        \n", "        print(\"\\n=== Structured Analysis ===\")\n", "        print(json.dumps(structured_data))\n", "        \n", "        # Validate with Pydantic\n", "        validated_analysis = DocumentAnalysis(**structured_data)\n", "        print(f\"\\nDocument Type: {validated_analysis.document_type}\")\n", "        print(f\"Confidence: {validated_analysis.confidence_score}\")\n", "    else:\n", "        print(\"Error: No structured output received\")\n", "        \n", "except Exception as e:\n", "    print(f\"Error occurred: {str(e)}\")"]}, {"cell_type": "markdown", "id": "481ae2cf", "metadata": {}, "source": ["# Bedrock - Converse - jpg - Structured"]}, {"cell_type": "code", "execution_count": null, "id": "6ed17574", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Structured Output:\n", "{\n", "    \"description\": \"The image contains text in a 12 point font size, intended to test OCR (optical character recognition) functionality across different file formats.\",\n", "    \"objects\": [\n", "        \"text\"\n", "    ],\n", "    \"colors\": [\n", "        \"black\"\n", "    ]\n", "}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_11678/672112781.py:74: PydanticDeprecatedSince20: The `dict` method is deprecated; use `model_dump` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/\n", "  print(json.dumps(image_analysis.dict(), indent=4))\n"]}], "source": ["import boto3\n", "import json\n", "from pydantic import BaseModel\n", "\n", "# Define the Pydantic model for the structured output\n", "class ImageAnalysis(BaseModel):\n", "    description: str\n", "    objects: list[str]\n", "    colors: list[str]\n", "\n", "# Initialize Bedrock client\n", "session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "bedrock_client = session.client(\"bedrock-runtime\", region_name=\"us-east-1\")\n", "\n", "# Define the system prompt with explicit JSON structure instructions\n", "system_prompts = [{\n", "    \"text\": \"\"\"You are an image analysis assistant. Analyze the provided image and return a JSON object with the following structure:\n", "{\n", "  \"description\": \"string (brief description of the image)\",\n", "  \"objects\": [\"string\", ...],\n", "  \"colors\": [\"string\", ...]\n", "}\n", "Ensure the output is valid JSON and contains only the JSON object, no additional text, explanations, or code block markers (e.g., ```json).\"\"\"\n", "}]\n", "\n", "# Load image as bytes\n", "image_path = \"image.png\"  # Replace with your image path\n", "try:\n", "    with open(image_path, \"rb\") as f:\n", "        image_bytes = f.read()\n", "    image_format = image_path.split(\".\")[-1].lower()  # Extract file extension (e.g., jpg)    \n", "except FileNotFoundError:\n", "    print(f\"Error: Image file not found at {image_path}\")\n", "    exit(1)\n", "\n", "# Ensure image format is supported\n", "supported_formats = [\"jpeg\", \"png\", \"gif\", \"webp\"]\n", "# if image_format not in supported_formats:\n", "#     print(f\"Error: Unsupported image format '{image_format}'. Supported formats: {supported_formats}\")\n", "#     exit(1)\n", "\n", "# User message with text and image\n", "user_message = {\n", "    \"role\": \"user\",\n", "    \"content\": [\n", "        {\"text\": \"Analyze the provided image and describe its contents.\"},\n", "        {\"image\": {\"format\": image_format, \"source\": {\"bytes\": image_bytes}}}\n", "    ]\n", "}\n", "\n", "# Make the Converse API call\n", "# try:\n", "response = bedrock_client.converse(\n", "    modelId=\"anthropic.claude-3-sonnet-********-v1:0\",\n", "    messages=[user_message],\n", "    system=system_prompts,\n", "    inferenceConfig={\n", "        \"maxTokens\": 512,\n", "        \"temperature\": 0.5,\n", "        \"topP\": 0.9\n", "    }\n", ")\n", "\n", "# Extract and validate the structured output\n", "output_message = response[\"output\"][\"message\"]\n", "for content in output_message[\"content\"]:\n", "    if \"text\" in content:\n", "        try:\n", "            # Parse the JSON output\n", "            json_output = json.loads(content[\"text\"])\n", "            # Validate with Pydantic\n", "            image_analysis = ImageAnalysis(**json_output)\n", "            print(\"Structured Output:\")\n", "            print(json.dumps(image_analysis.dict(), indent=4))\n", "        except json.JSONDecodeError:\n", "            print(\"Error: Model did not return valid JSON\")\n", "        except:\n", "            print(\"Error: Pydantic validation failed - {e}\")\n", "# except:\n", "#     pass"]}, {"cell_type": "markdown", "id": "9f2676e8", "metadata": {}, "source": ["# Actual"]}, {"cell_type": "code", "execution_count": 33, "id": "70f7933e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error: <PERSON> did not return valid JSON\n"]}], "source": ["import boto3\n", "import json\n", "from pydantic import BaseModel\n", "\n", "# Define the Pydantic model for the structured output\n", "class ImageAnalysis(BaseModel):\n", "    description: str\n", "    objects: list[str]\n", "    colors: list[str]\n", "\n", "# Initialize Bedrock client\n", "session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "bedrock_client = session.client(\"bedrock-runtime\", region_name=\"us-east-1\")\n", "\n", "# Define the system prompt with explicit JSON structure instructions\n", "system_prompts = [{\n", "    \"text\": \"\"\"Give output in format specified in toolspec\"\"\"\n", "}]\n", "\n", "# Load image as bytes\n", "image_path = \"image.png\"  # Replace with your image path\n", "try:\n", "    with open(image_path, \"rb\") as f:\n", "        image_bytes = f.read()\n", "    image_format = image_path.split(\".\")[-1].lower()  # Extract file extension (e.g., jpg)\n", "except FileNotFoundError:\n", "    print(f\"Error: Image file not found at {image_path}\")\n", "    exit(1)\n", "\n", "# Ensure image format is supported\n", "supported_formats = [\"jpeg\", \"png\", \"gif\", \"webp\"]\n", "# if image_format not in supported_formats:\n", "#     print(f\"Error: Unsupported image format '{image_format}'. Supported formats: {supported_formats}\")\n", "#     exit(1)\n", "\n", "# User message with text and image\n", "user_message = {\n", "    \"role\": \"user\",\n", "    \"content\": [\n", "        {\"text\": \"Analyze the provided image and describe its contents.\"},\n", "        {\"image\": {\"format\": image_format, \"source\": {\"bytes\": image_bytes}}}\n", "    ]\n", "}\n", "\n", "# Make the Converse API call\n", "# try:\n", "response = bedrock_client.converse(\n", "    modelId=\"anthropic.claude-3-sonnet-********-v1:0\",\n", "    messages=[user_message],\n", "    system=system_prompts,\n", "    toolConfig={\"tools\": tool_list},\n", "    inferenceConfig={\n", "        \"maxTokens\": 512,\n", "        \"temperature\": 0.5,\n", "        \"topP\": 0.9\n", "    }\n", ")\n", "\n", "# Extract and validate the structured output\n", "output_message = response[\"output\"][\"message\"]\n", "for content in output_message[\"content\"]:\n", "    if \"text\" in content:\n", "        try:\n", "            # Parse the JSON output\n", "            json_output = json.loads(content[\"text\"])\n", "            # Validate with Pydantic\n", "            image_analysis = ImageAnalysis(**json_output)\n", "            print(\"Structured Output:\")\n", "            print(json.dumps(image_analysis.dict(), indent=4))\n", "        except json.JSONDecodeError:\n", "            print(\"Error: Model did not return valid JSON\")\n", "        except:\n", "            print(\"Error: Pydantic validation failed - {e}\")\n", "# except:\n", "#     pass\n", "print(content[\"text\"])"]}, {"cell_type": "code", "execution_count": 37, "id": "1d9de5bf", "metadata": {}, "outputs": [], "source": ["# Extract the structured output\n", "output_message = response[\"output\"][\"message\"]\n", "for content in output_message[\"content\"]:\n", "    if \"toolUse\" in content:\n", "        tool_result = content[\"toolUse\"][\"input\"]\n", "        print(\"Structured Output:\")\n", "        print(json.dumps(tool_result, indent=4))"]}, {"cell_type": "code", "execution_count": 40, "id": "9ddaa5d4", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'text': 'The provided image contains text written in a 12-point font size. The text appears to be a sample used for testing OCR (Optical Character Recognition) code and its ability to work on different file formats. The text repeats the well-known phrase \"The quick brown dog jumped over the lazy fox\" multiple times. This phrase is commonly used for testing purposes as it contains all the letters of the English alphabet. The image does not contain any identifiable individuals or personal information.'}"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["output_message[\"content\"][0]"]}, {"cell_type": "code", "execution_count": null, "id": "f8702651", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "257e7a58", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from mypy_extensions import DefaultArg"]}, {"cell_type": "code", "execution_count": null, "id": "27f94e9a", "metadata": {}, "outputs": [], "source": ["from "]}, {"cell_type": "code", "execution_count": 1, "id": "84111aee", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'AgentsforBedrockRuntime' object has no attribute 'converse'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 25\u001b[0m\n\u001b[1;32m     20\u001b[0m messages \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m     21\u001b[0m     {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m<PERSON>e\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: [{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtext\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mSummarize the topic: Amazon Bedrock\u001b[39m\u001b[38;5;124m\"\u001b[39m}]}\n\u001b[1;32m     22\u001b[0m ]\n\u001b[1;32m     24\u001b[0m \u001b[38;5;66;03m# Call the Converse API\u001b[39;00m\n\u001b[0;32m---> 25\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[43mclient\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconverse\u001b[49m(\n\u001b[1;32m     26\u001b[0m     modelId\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124manthropic.claude-3-sonnet-********-v1:0\u001b[39m\u001b[38;5;124m\"\u001b[39m,  \u001b[38;5;66;03m# replace with your actual model ID\u001b[39;00m\n\u001b[1;32m     27\u001b[0m     conversationState\u001b[38;5;241m=\u001b[39m{\n\u001b[1;32m     28\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m: messages\n\u001b[1;32m     29\u001b[0m     },\n\u001b[1;32m     30\u001b[0m     toolConfig\u001b[38;5;241m=\u001b[39m{\n\u001b[1;32m     31\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtools\u001b[39m\u001b[38;5;124m\"\u001b[39m: [],\n\u001b[1;32m     32\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstructure\u001b[39m\u001b[38;5;124m\"\u001b[39m: structure,\n\u001b[1;32m     33\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124menforceStructure\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28;01mTrue\u001b[39;00m   \u001b[38;5;66;03m# important: force the model to follow the structure \u001b[39;00m\n\u001b[1;32m     34\u001b[0m     }\n\u001b[1;32m     35\u001b[0m )\n\u001b[1;32m     37\u001b[0m \u001b[38;5;66;03m# Extract and print the structured JSON response\u001b[39;00m\n\u001b[1;32m     38\u001b[0m structured_output \u001b[38;5;241m=\u001b[39m response\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124moutput\u001b[39m\u001b[38;5;124m\"\u001b[39m, {})\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m, [{}])[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtext\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/Documents/repositories/reference_code/.venv/lib/python3.10/site-packages/botocore/client.py:965\u001b[0m, in \u001b[0;36mBaseClient.__getattr__\u001b[0;34m(self, item)\u001b[0m\n\u001b[1;32m    962\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m event_response \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    963\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m event_response\n\u001b[0;32m--> 965\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAttribute<PERSON>rror\u001b[39;00m(\n\u001b[1;32m    966\u001b[0m     \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m object has no attribute \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mitem\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    967\u001b[0m )\n", "\u001b[0;31mAttributeError\u001b[0m: 'AgentsforBedrockRuntime' object has no attribute 'converse'"]}], "source": ["import boto3\n", "import json\n", "\n", "session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "\n", "# Create Bedrock Agent Runtime client\n", "client = session.client(\"bedrock-agent-runtime\")\n", "\n", "# Define your structure: e.g., want a JSON object with title and summary fields\n", "structure = {\n", "    \"type\": \"object\",\n", "    \"properties\": {\n", "        \"title\": {\"type\": \"string\"},\n", "        \"summary\": {\"type\": \"string\"}\n", "    },\n", "    \"required\": [\"title\", \"summary\"]\n", "}\n", "\n", "# Prepare messages (standard chat format)\n", "messages = [\n", "    {\"role\": \"user\", \"content\": [{\"text\": \"Summarize the topic: Amazon Bedrock\"}]}\n", "]\n", "\n", "# Call the Converse API\n", "response = client.converse(\n", "    modelId=\"anthropic.claude-3-sonnet-********-v1:0\",  # replace with your actual model ID\n", "    conversationState={\n", "        \"messages\": messages\n", "    },\n", "    toolConfig={\n", "        \"tools\": [],\n", "        \"structure\": structure,\n", "        \"enforceStructure\": True   # important: force the model to follow the structure \n", "    }\n", ")\n", "\n", "# Extract and print the structured JSON response\n", "structured_output = response.get(\"output\", {}).get(\"content\", [{}])[0].get(\"text\", \"\")\n", "print(\"Raw structured output text:\", structured_output)\n", "\n", "# Parse if needed\n", "try:\n", "    parsed = json.loads(structured_output)\n", "    print(\"Parsed JSON output:\", json.dumps(parsed, indent=2))\n", "except json.JSONDecodeError:\n", "    print(\"Could not parse response as JSO<PERSON>:\", structured_output)\n"]}, {"cell_type": "code", "execution_count": null, "id": "c26e556a", "metadata": {}, "outputs": [], "source": ["hiu"]}, {"cell_type": "markdown", "id": "a6608f68", "metadata": {}, "source": ["# Medium - converse - structured output (Pydentic) - image"]}, {"cell_type": "code", "execution_count": 45, "id": "258eceb2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install  -qU boto3 pydantic requests beautifulsoup4"]}, {"cell_type": "code", "execution_count": 46, "id": "e7cf7b4f", "metadata": {}, "outputs": [], "source": ["import boto3\n", "\n", "session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "\n", "client = session.client(\"bedrock-runtime\")"]}, {"cell_type": "code", "execution_count": 47, "id": "f47ab03d", "metadata": {}, "outputs": [], "source": ["from typing import Optional, Type, Dict, Any\n", "from pydantic import BaseModel\n", "\n", "def convert_pydantic_to_bedrock_tool(\n", "    model: Type[BaseModel],\n", "    description: Optional[str] = None\n", ") -> Dict[str, Any]:\n", "    \"\"\"\n", "    Converts a Pydantic model to a tool description for the Amazon Bedrock Converse API.\n", "    \n", "    Args:\n", "        model: The Pydantic model class to convert\n", "        description: Optional description of the tool's purpose\n", "\n", "    Returns:\n", "        Dict containing the Bedrock tool specification        \n", "    \"\"\"\n", "    # Validate input model\n", "    if not isinstance(model, type) or not issubclass(model, BaseModel):\n", "        raise ValueError(\"Input must be a Pydantic model class\")\n", "    \n", "    name = model.__name__\n", "    input_schema = model.model_json_schema()\n", "    tool = {\n", "        'toolSpec': {\n", "            'name': name,\n", "            'description': description or f\"{name} Tool\",\n", "            'inputSchema': {'json': input_schema }\n", "        }\n", "    }\n", "    return tool\n"]}, {"cell_type": "code", "execution_count": 73, "id": "576be4b5", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'input_schema' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[73], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43minput_schema\u001b[49m\n", "\u001b[0;31mNameError\u001b[0m: name 'input_schema' is not defined"]}], "source": ["input_schema"]}, {"cell_type": "code", "execution_count": 66, "id": "e22fad63", "metadata": {}, "outputs": [], "source": ["from pydantic import Field\n", "\n", "class Joke(BaseModel):\n", "    content: str = Field(description=\"content inside the image\")\n", "    punchline: str = Field(description=\"The punchline of image. if not given the generate\")\n", "    sentiment: str = Field(description=\"The sentiment of the joke\")\n", "\n", "joke_extraction = convert_pydantic_to_bedrock_tool(Joke)\n"]}, {"cell_type": "code", "execution_count": 72, "id": "2329e708", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'toolSpec': {'description': '<PERSON><PERSON>',\n", "              'inputSchema': {'json': {'properties': {'content': {'description': 'content '\n", "                                                                                 'inside '\n", "                                                                                 'the '\n", "                                                                                 'image',\n", "                                                                  'title': 'Content',\n", "                                                                  'type': 'string'},\n", "                                                      'punchline': {'description': 'The '\n", "                                                                                   'punchline '\n", "                                                                                   'of '\n", "                                                                                   'image. '\n", "                                                                                   'if '\n", "                                                                                   'not '\n", "                                                                                   'given '\n", "                                                                                   'the '\n", "                                                                                   'generate',\n", "                                                                    'title': 'Punchline',\n", "                                                                    'type': 'string'},\n", "                                                      'sentiment': {'description': 'The '\n", "                                                                                   'sentiment '\n", "                                                                                   'of '\n", "                                                                                   'the '\n", "                                                                                   'joke',\n", "                                                                    'title': 'Sentiment',\n", "                                                                    'type': 'string'}},\n", "                                       'required': ['content',\n", "                                                    'punchline',\n", "                                                    'sentiment'],\n", "                                       'title': '<PERSON><PERSON>',\n", "                                       'type': 'object'}},\n", "              'name': '<PERSON><PERSON>'}}\n"]}], "source": ["pprint(joke_extraction)"]}, {"cell_type": "code", "execution_count": 67, "id": "fa88d3b4", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'content': 'The image shows a block of 12 point text that appears to be testing OCR (optical character recognition) code to see if it works on different file formats. The text repeats the phrase \"The quick brown dog jumped over the lazy fox\" multiple times.',\n", " 'punchline': \"I guess they wanted to make sure the OCR didn't get too dog-tired from all that jumping!\",\n", " 'sentiment': 'Silly'}"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["system_prompts = [{\n", "    \"text\": \"\"\"Answer in one word only\"\"\"\n", "}]\n", "\n", "messages = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": [\n", "            {\"text\": \"Analyze the provided image and describe its contents.\"},\n", "            {\"image\": {\"format\": image_format, \"source\": {\"bytes\": image_bytes}}}\n", "        ]\n", "    }\n", "]\n", "\n", "tools = [joke_extraction]\n", "\n", "response = client.converse(\n", "    modelId=\"anthropic.claude-3-sonnet-********-v1:0\",\n", "    messages=messages,\n", "    system=system_prompts,\n", "    inferenceConfig={\n", "        'maxTokens': 4096,\n", "        'temperature': 0,\n", "        'topP': 1,\n", "    },\n", "    toolConfig={\n", "        \"tools\": tools,\n", "        \"toolChoice\": {\n", "            \"tool\":{\"name\":\"<PERSON><PERSON>\"}\n", "        }\n", "    },\n", ")\n", "\n", "output = response['output']['message']['content'][0]['toolUse']['input']\n", "output\n"]}, {"cell_type": "code", "execution_count": 74, "id": "aa9dcf1f", "metadata": {}, "outputs": [], "source": ["from typing import Any\n", "from pydantic import BaseModel\n", "\n", "class PrintAllCharacteristicsInput(BaseModel):\n", "    \"\"\"Input model for print_all_characteristics function.\"\"\"\n", "    class Config:\n", "        extra = \"allow\"  # This corresponds to additionalProperties: true\n", "\n", "class PrintAllCharacteristics(BaseModel):\n", "    \"\"\"Main model representing the complete schema.\"\"\"\n", "    name: str = \"print_all_characteristics\"\n", "    description: str = \"Prints all characteristics which are provided.\"\n", "    input_schema: PrintAllCharacteristicsInput\n", "\n", "characteristics_tool = convert_pydantic_to_bedrock_tool(PrintAllCharacteristics, description=\"Prints all characteristics which are provided.\")\n", "\n", "query = f\"\"\"Given a description of a character, your task is to extract all the characteristics of the character and print them using the print_all_characteristics tool.\n", "\n", "The print_all_characteristics tool takes an arbitrary number of inputs where the key is the characteristic name and the value is the characteristic value (age: 28 or eye_color: green).\n", "\n", "<description>\n", "The man is tall, with a beard and a scar on his left cheek. He has a deep voice and wears a black leather jacket.\n", "</description>\n", "\n", "Now use the print_all_characteristics tool.\"\"\"\n", "\n", "messages = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": [{\"text\": query}],\n", "    }\n", "]\n", "\n", "tools = [characteristics_tool]\n", "\n", "response = client.converse(\n", "    #modelId=\"anthropic.claude-3-haiku-20240307-v1:0\",\n", "    modelId=\"us.anthropic.claude-3-5-haiku-20241022-v1:0\",\n", "    messages=messages,\n", "    inferenceConfig={\n", "        'maxTokens': 4096,\n", "        'temperature': 0,\n", "        'topP': 1,\n", "    },\n", "    toolConfig={\n", "        \"tools\": tools,\n", "        \"toolChoice\": {\n", "            \"tool\":{\"name\":\"PrintAllCharacteristics\"}\n", "        }\n", "    },\n", ")\n", "\n", "output = response['output']['message']['content'][0]['toolUse']['input']\n"]}, {"cell_type": "code", "execution_count": 77, "id": "668da3ef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'toolSpec': {'description': 'Prints all characteristics which are provided.',\n", "              'inputSchema': {'json': {'$defs': {'PrintAllCharacteristicsInput': {'additionalProperties': True,\n", "                                                                                  'description': 'Input '\n", "                                                                                                 'model '\n", "                                                                                                 'for '\n", "                                                                                                 'print_all_characteristics '\n", "                                                                                                 'function.',\n", "                                                                                  'properties': {},\n", "                                                                                  'title': 'PrintAllCharacteristicsInput',\n", "                                                                                  'type': 'object'}},\n", "                                       'description': 'Main model representing '\n", "                                                      'the complete schema.',\n", "                                       'properties': {'description': {'default': 'Prints '\n", "                                                                                 'all '\n", "                                                                                 'characteristics '\n", "                                                                                 'which '\n", "                                                                                 'are '\n", "                                                                                 'provided.',\n", "                                                                      'title': 'Description',\n", "                                                                      'type': 'string'},\n", "                                                      'input_schema': {'$ref': '#/$defs/PrintAllCharacteristicsInput'},\n", "                                                      'name': {'default': 'print_all_characteristics',\n", "                                                               'title': 'Name',\n", "                                                               'type': 'string'}},\n", "                                       'required': ['input_schema'],\n", "                                       'title': 'PrintAllCharacteristics',\n", "                                       'type': 'object'}},\n", "              'name': 'PrintAllCharacteristics'}}\n"]}], "source": ["pprint(characteristics_tool)"]}, {"cell_type": "markdown", "id": "dc0c0740", "metadata": {}, "source": ["# Medium - converse - structured output (json) - image"]}, {"cell_type": "code", "execution_count": 78, "id": "3c0764dc", "metadata": {}, "outputs": [], "source": ["import boto3\n", "\n", "session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "\n", "client = session.client(\"bedrock-runtime\")"]}, {"cell_type": "code", "execution_count": 86, "id": "e2f7ef73", "metadata": {}, "outputs": [], "source": ["system_prompts = [{\n", "    \"text\": \"Answer in gujarati language\"\n", "}]\n", "\n", "messages = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": [\n", "            {\"text\": \"Analyze the provided image and describe its contents.\"},\n", "            {\"image\": {\"format\": image_format, \"source\": {\"bytes\": image_bytes}}}\n", "        ]\n", "    }\n", "]\n", "\n", "toolConfig={\n", "    \"tools\": [\n", "        {\n", "            'toolSpec': {\n", "                'name': '<PERSON><PERSON>',\n", "                'description': '<PERSON><PERSON>',\n", "                'inputSchema': {\n", "                    'json': {\n", "                        'title': '<PERSON><PERSON>',\n", "                        'type': 'object',\n", "                        'properties': {\n", "                            'content': {\n", "                                'title': 'Content',\n", "                                'type': 'string',\n", "                                'description': 'content inside the image'\n", "                            },\n", "                            'punchline': {\n", "                                'description': 'The punchline of image. ',\n", "                                'title': 'Punchline',\n", "                                'type': 'string'\n", "                            },\n", "                            'sentiment': {\n", "                                'description': 'The sentiment',\n", "                                'title': 'Sentiment',\n", "                                'type': 'string'\n", "                            }\n", "                        },\n", "                        'required': [\n", "                            'content',\n", "                            'punchline',\n", "                            'sentiment'\n", "                        ]\n", "                    }\n", "                },\n", "            }\n", "        }\n", "    ],\n", "    \"toolChoice\": {\n", "        \"tool\":{\"name\":\"<PERSON><PERSON>\"}\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 2, "id": "5b951fc7", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'system_prompts' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[2], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m response \u001b[38;5;241m=\u001b[39m client\u001b[38;5;241m.\u001b[39mconverse(\n\u001b[1;32m      2\u001b[0m     modelId\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124manthropic.claude-3-sonnet-********-v1:0\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m----> 3\u001b[0m     system\u001b[38;5;241m=\u001b[39m\u001b[43msystem_prompts\u001b[49m,\n\u001b[1;32m      4\u001b[0m     messages\u001b[38;5;241m=\u001b[39mmessages,\n\u001b[1;32m      5\u001b[0m     inferenceConfig\u001b[38;5;241m=\u001b[39m{\n\u001b[1;32m      6\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmaxTokens\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m4096\u001b[39m,\n\u001b[1;32m      7\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtemperature\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m0\u001b[39m,\n\u001b[1;32m      8\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtopP\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m1\u001b[39m,\n\u001b[1;32m      9\u001b[0m     },\n\u001b[1;32m     10\u001b[0m     toolConfig\u001b[38;5;241m=\u001b[39mtoolConfig,\n\u001b[1;32m     11\u001b[0m )\n\u001b[1;32m     13\u001b[0m output \u001b[38;5;241m=\u001b[39m response[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124moutput\u001b[39m\u001b[38;5;124m'\u001b[39m][\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmessage\u001b[39m\u001b[38;5;124m'\u001b[39m][\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m'\u001b[39m][\u001b[38;5;241m0\u001b[39m][\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtoolUse\u001b[39m\u001b[38;5;124m'\u001b[39m][\u001b[38;5;124m'\u001b[39m\u001b[38;5;124minput\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[1;32m     14\u001b[0m pprint(output)\n", "\u001b[0;31mNameError\u001b[0m: name 'system_prompts' is not defined"]}], "source": ["response = client.converse(\n", "    modelId=\"anthropic.claude-3-sonnet-********-v1:0\",\n", "    system=system_prompts,\n", "    messages=messages,\n", "    inferenceConfig={\n", "        'maxTokens': 4096,\n", "        'temperature': 0,\n", "        'topP': 1,\n", "    },\n", "    toolConfig=toolConfig,\n", ")\n", "\n", "output = response['output']['message']['content'][0]['toolUse']['input']\n", "pprint(output)"]}, {"cell_type": "code", "execution_count": 89, "id": "f4186f3f", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'toolUse': {'toolUseId': 'tooluse_x5K7PQ58RKOGkcZ8DhAOww',\n", "  'name': '<PERSON><PERSON>',\n", "  'input': {'content': 'The provided image contains text in 12 point font size. It appears to be testing OCR (Optical Character Recognition) code to see if it works on different file formats. The text repeats the phrase \"The quick brown dog jumped over the lazy fox\" multiple times.',\n", "   'punchline': \"This image doesn't seem to contain a joke or punchline. It's just sample text used for testing purposes.\",\n", "   'sentiment': 'Neutral'}}}"]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["response['output']['message']['content'][0]"]}, {"cell_type": "code", "execution_count": null, "id": "39baae40", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "533e6ebc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7c42719f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cdd89f8a", "metadata": {}, "outputs": [], "source": ["\n"]}, {"cell_type": "markdown", "id": "c1ca256d", "metadata": {}, "source": ["# Extraction "]}, {"cell_type": "code", "execution_count": 92, "id": "5d3d152a", "metadata": {}, "outputs": [], "source": ["# reading page1_doc1_2526 TABBED APP 4 (2) (1).json file\n", "import json\n", "with open('page1_doc1_2526 TABBED APP 4 (2) (1).json', 'r') as f:\n", "    response = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "id": "20cd9edf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== NON-TABLE TEXT ===\n", "CHOSPEDALES\n", "ACORD®\n", "CALIFORNIA COMMERCIAL INSURANCE APPLICATION\n", "DATE (MM/DD/YYYY)\n", "APPLICANT INFORMATION SECTION\n", "05/20/2025\n", "PRODUCER\n", "CARRIER\n", "NAIC CODE\n", "IMA, Inc. - Pasadena\n", "MARKETING COMPANY - USE ONLY WHEN CARRIER IS TBD\n", "N/A\n", "3475 E. Foothill Boulevard\n", "Suite 100\n", "COMPANY POLICY OR PROGRAM NAME\n", "PROGRAM CODE\n", "Pasadena, CA 91107\n", "POLICY NUMBER\n", "TBD\n", "CONTACT\n", "NAME:\n", "UNDERWRITER\n", "UNDERWRITER OFFICE\n", "PHONE\n", "(A/C, No, Ext):\n", "**************\n", "FAX\n", "(A/C, No):\n", "**************\n", "QUOTE\n", "ISSUE POLICY\n", "RENEW\n", "E-MAIL\n", "STATUS OF\n", "ADDRESS:\n", "TRANSACTION\n", "BOUND (Give Date and/or Attach Copy):\n", "CODE:\n", "SUBCODE:\n", "CHANGE\n", "DATE\n", "TIME\n", "AM\n", "AGENCY CUSTOMER ID: MERCLAN-C2\n", "CANCEL\n", "PM\n", "LINES OF BUSINESS\n", "INDICATE LINES OF BUSINESS\n", "PREMIUM\n", "PREMIUM\n", "PREMIUM\n", "BOILER & MACHINERY\n", "$\n", "CYBER AND PRIVACY\n", "$\n", "YACHT\n", "$\n", "BUSINESS AUTO\n", "$\n", "FIDUCIARY LIABILITY\n", "$\n", "$\n", "BUSINESS OWNERS\n", "$\n", "GARAGE AND DEALERS\n", "$\n", "$\n", "COMMERCIAL GENERAL LIABILITY\n", "$\n", "LIQUOR LIABILITY\n", "$\n", "$\n", "COMMERCIAL INLAND MARINE\n", "$\n", "MOTOR CARRIER\n", "$\n", "$\n", "COMMERCIAL PROPERTY\n", "$\n", "TRUCKERS\n", "$\n", "$\n", "CRIME\n", "$\n", "UMBRELLA\n", "$\n", "$\n", "ATTACHMENTS\n", "ACCOUNTS RECEIVABLE / VALUABLE PAPERS\n", "GLASS AND SIGN SECTION\n", "STATEMENT / SCHEDULE OF VALUES\n", "ADDITIONAL INTEREST SCHEDULE\n", "HOTEL / MOTEL SUPPLEMENT\n", "STATE SUPPLEMENT (If applicable)\n", "ADDITIONAL PREMISES INFORMATION SCHEDULE\n", "INSTALLATION / BUILDERS RISK SECTION\n", "VACANT BUILDING SUPPLEMENT\n", "APARTMENT BUILDING SUPPLEMENT\n", "INTERNATIONAL LIABILITY EXPOSURE SUPPLEMENT\n", "VEHICLE SCHEDULE\n", "CONDO ASSN BYLAWS (for D&O Coverage only)\n", "INTERNATIONAL PROPERTY EXPOSURE SUPPLEMENT\n", "CONTRACTORS SUPPLEMENT\n", "LOSS SUMMARY\n", "COVERAGES SCHEDULE\n", "OPEN CARGO SECTION\n", "DEALERS SECTION\n", "PREMIUM PAYMENT SUPPLEMENT\n", "DRIVER INFORMATION SCHEDULE\n", "PROFESSIONAL LIABILITY SUPPLEMENT\n", "ELECTRONIC DATA PROCESSING SECTION\n", "RESTAURANT / TAVERN SUPPLEMENT\n", "POLICY INFORMATION\n", "MINIMUM\n", "PROPOSED EFF DATE\n", "PROPOSED EXP DATE\n", "BILLING PLAN\n", "PAYMENT PLAN\n", "METHOD OF PAYMENT\n", "AUDIT\n", "DEPOSIT\n", "PREMIUM\n", "POLICY PREMIUM\n", "07/01/2025\n", "07/01/2026\n", "AGENCY\n", "MO\n", "$\n", "$\n", "$\n", "DIRECT\n", "APPLICANT INFORMATION\n", "NAME (First Named Insured) AND MAILING ADDRESS (including ZIP+4)\n", "GL CODE\n", "SIC\n", "NAICS\n", "FEIN OR SOC SEC #\n", "Merchants Landscape Services, Inc.\n", "0782\n", "561730\n", "95-4725606\n", "1190 Monterey Pass Road\n", "BUSINESS\n", "PHONE\n", "#: (*************\n", "Monterey Park, CA 91754\n", "WEBSITE ADDRESS\n", "https://www.merchantslandscape.com/\n", "CORPORATION\n", "JOINT VENTURE\n", "NOT FOR PROFIT ORG\n", "SUBCHAPTER \"S\" CORPORATION\n", "NO. OF MEMBERS\n", "INDIVIDUAL\n", "LLC\n", "AND MANAGERS:\n", "PARTNERSHIP\n", "TRUST\n", "Client\n", "NAME (Other Named Insured) AND MAILING ADDRESS (including ZIP+4)\n", "GL CODE\n", "SIC\n", "NAICS\n", "FEIN OR SOC SEC #\n", "Merchants Landscape Services, Inc.\n", "97047\n", "0782\n", "561730\n", "95-4725606\n", "1190 Monterey Pass Road\n", "Monterey Park, CA 91754\n", "BUSINESS PHONE #:\n", "************** 609\n", "WEBSITE ADDRESS\n", "CORPORATION\n", "JOINT VENTURE\n", "NOT FOR PROFIT ORG\n", "SUBCHAPTER \"S\" CORPORATION\n", "Commercial contractor landscaper, no r\n", "NO. OF MEMBERS\n", "INDIVIDUAL\n", "LLC\n", "AND MANAGERS:\n", "PARTNERSHIP\n", "TRUST\n", "NAME (Other Named Insured) AND MAILING ADDRESS (including ZIP+4)\n", "GL CODE\n", "SIC\n", "NAICS\n", "FEIN OR SOC SEC #\n", "Merchants Landscaping, Inc.\n", "1190 Monterey Pass Road\n", "Monterey Park, CA 91754\n", "BUSINESS PHONE #:\n", "WEBSITE ADDRESS\n", "CORPORATION\n", "JOINT VENTURE\n", "NOT FOR PROFIT ORG\n", "SUBCHAPTER \"S\" CORPORATION\n", "INDIVIDUAL\n", "LLC\n", "NO. OF MEMBERS\n", "PARTNERSHIP\n", "TRUST\n", "AND MANAGERS:\n", "ACORD 125 CA (2023/01)\n", "Page 1 of 4\n", "© 2022 ACORD CORPORATION. All rights reserved.\n", "The ACORD name and logo are registered marks of ACORD\n", "\n", "=== TABLE 1 ===\n", "COMPANY POLICY OR PROGRAM NAME,PROGRAM CODE\n", ",\n", "\n", "=== TABLE 2 ===\n", "UNDERWRITER,UNDERWRITER OFFICE\n", ",\n", "\n", "=== TABLE 3 ===\n", ",,QUOTE,,,,ISSUE,POLICY,,RENEW,\n", "STATUS OF TRANSACTION,,BOUND,(Give Date,,and/or,Attach,Copy):,,,\n", ",,CHANGE,,DATE,,,TIME,,,AM\n", ",,CANCEL,,,,,,,,PM\n", "\n", "=== TABLE 4 ===\n", ",INDICATE LINES OF BUSINESS,PREMIUM\n", ",BOILER & MACHINERY,$\n", ",BUSINESS AUTO,$\n", ",BUSINESS OWNERS,$\n", ",COMMERCIAL GENERAL LIABILITY,$\n", ",COMMERCIAL INLAND MARINE,$\n", ",COMMERCIAL PROPERTY,$\n", ",CRIME,$\n", "\n", "=== TABLE 5 ===\n", ",,PREMIUM\n", ",CYBER AND PRIVACY,$\n", ",FIDUCIARY LIABILITY,$\n", ",GARAGE AND DEALERS,$\n", ",LIQUOR LIABILITY,$\n", ",MOTOR CARRIER,$\n", ",TRUCKERS,$\n", ",UMBRELLA,$\n", "\n", "=== TABLE 6 ===\n", ",,PREMIUM\n", ",YACHT,$\n", ",,$\n", ",,$\n", ",,$\n", ",,$\n", ",,$\n", ",,$\n", "\n", "=== TABLE 7 ===\n", ",ACCOUNTS RECEIVABLE / VALUABLE PAPERS,,<PERSON><PERSON><PERSON> AND SIGN SECTION,,STATEMENT / SCHEDULE OF VALUES\n", ",ADDITIONAL INTEREST SCHEDULE,,HOTEL / MOTEL SUPPLEMENT,,STATE SUPPLEMENT (If applicable)\n", ",ADDITIONAL PREMISES INFORMATION SCHEDULE,,INSTALLATION / BUILDERS RISK SECTION,,VACANT BUILDING SUPPLEMENT\n", ",APARTMENT BUILDING SUPPLEMENT,,INTERNATIONAL LIABILITY EXPOSURE SUPPLEMENT,,VEH<PERSON><PERSON> SCHEDULE\n", ",CONDO ASSN BYLAWS (for D&O Coverage only),,INTERNATIONAL PROPERTY EXPOSURE SUPPLEMENT,,\n", ",CONTRACTORS SUPPLEMENT,,LOSS SUMMARY,,\n", ",COVERAGES SCHEDULE,,OPEN CARGO SECTION,,\n", ",DEALERS SECTION,,PREMIUM PAYMENT SUPPLEMENT,,\n", ",DRIVER INFORMATION SCHEDULE,,PROFESSIONAL LIABILITY SUPPLEMENT,,\n", ",ELECTRONIC DATA PROCESSING SECTION,,RESTAURANT / TAVERN SUPPLEMENT,,\n", "\n", "=== TABLE 8 ===\n", "PROPOSED EFF DATE,PROPOSED EXP DATE,B<PERSON><PERSON>ING,PLAN,,PAYMENT PLAN,METHOD OF PAYMENT,AUDIT,DEPOSIT,MINIMUM PREMIUM,POLICY PREMIUM\n", "07/01/2025,07/01/2026,DIRECT,,AGENCY,MO,,,$,$,$\n", "\n", "=== TABLE 9 ===\n", "GL CODE,SIC,NAICS,FEIN OR SOC SEC #\n", ",0782,561730,95-4725606\n", "\n", "=== TABLE 10 ===\n", ",CORPORATION,,JOINT VENTURE,,NOT FOR PROFIT ORG,,\"SUBCHAPTER \"\"S\"\" CORPORATION\",,\n", ",INDIVIDUAL,,NO. OF MEMBERS LLC AND MANAGERS:,,PARTNERSHIP,,TRUST,,\n", "\n", "=== TABLE 11 ===\n", "GL CODE,SIC,NAICS,FEIN OR SOC SEC #\n", "97047,0782,561730,95-4725606\n", "\n", "=== TABLE 12 ===\n", ",CORPORATION,,JOINT VENTURE,,NOT FOR PROFIT ORG,,\"SUBCHAPTER \"\"S\"\" CORPORATION\",,\"Commercial contractor landscaper, no r\"\n", ",INDIVIDUAL,,NO. OF MEMBERS LLC AND MANAGERS:,,PARTNERSHIP,,TRUST,,\n", "\n", "=== TABLE 13 ===\n", "GL CODE,SIC,NAICS,FEIN OR SOC SEC #\n", ",,,\n", "\n", "=== TABLE 14 ===\n", ",CORPORATION,,JOINT VENTURE,,NOT FOR PROFIT ORG,,\"SUBCHAPTER \"\"S\"\" CORPORATION\",,\n", ",INDIVIDUAL,,LLC NO. OF MEMBERS AND MANAGERS:,,PARTNERSHIP,,TRUST,,\n", "\n"]}], "source": ["simple_text, structured_text = extract_text_from_textract_response(response)\n", "print(structured_text)\n", "\n", "# Read and encode the image\n", "with open('page1_doc1_2526 TABBED APP 4 (2) (1).jpg', \"rb\") as image_file:\n", "    content_image = base64.b64encode(image_file.read()).decode('utf8')"]}, {"cell_type": "code", "execution_count": null, "id": "9e4c4c94", "metadata": {}, "outputs": [{"data": {"text/plain": ["'jpg'"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["image_format"]}, {"cell_type": "markdown", "id": "dde6bb0d", "metadata": {}, "source": ["# Complete working"]}, {"cell_type": "code", "execution_count": 170, "id": "eaebe029", "metadata": {}, "outputs": [{"data": {"text/plain": ["'=== NON-TABLE TEXT ===\\nCHOSPEDALES\\nACORD®\\nCALIFORNIA COMMERCIAL INSURANCE APPLICATION\\nDATE (MM/DD/YYYY)\\nAPPLICANT INFORMATION SECTION\\n05/20/2025\\nPRODUCER\\nCARRIER\\nNAIC CODE\\nIMA, Inc. - Pasadena\\nMARKETING COMPANY - USE ONLY WHEN CARRIER IS TBD\\nN/A\\n3475 E. Foothill Boulevard\\nSuite 100\\nCOMPANY POLICY OR PROGRAM NAME\\nPROGRAM CODE\\nPasadena, CA 91107\\nPOLICY NUMBER\\nTBD\\nCONTACT\\nNAME:\\nUNDERWRITER\\nUNDERWRITER OFFICE\\nPHONE\\n(A/C, No, Ext):\\n**************\\nFAX\\n(A/C, No):\\n**************\\nQUOTE\\nISSUE POLICY\\nRENEW\\nE-MAIL\\nSTATUS OF\\nADDRESS:\\nTRANSACTION\\nBOUND (Give Date and/or Attach Copy):\\nCODE:\\nSUBCODE:\\nCHANGE\\nDATE\\nTIME\\nAM\\nAGENCY CUSTOMER ID: MERCLAN-C2\\nCANCEL\\nPM\\nLINES OF BUSINESS\\nINDICATE LINES OF BUSINESS\\nPREMIUM\\nPREMIUM\\nPREMIUM\\nBOILER & MACHINERY\\n$\\nCYBER AND PRIVACY\\n$\\nYACHT\\n$\\nBUSINESS AUTO\\n$\\nFIDUCIARY LIABILITY\\n$\\n$\\nBUSINESS OWNERS\\n$\\nGARAGE AND DEALERS\\n$\\n$\\nCOMMERCIAL GENERAL LIABILITY\\n$\\nLIQUOR LIABILITY\\n$\\n$\\nCOMMERCIAL INLAND MARINE\\n$\\nMOTOR CARRIER\\n$\\n$\\nCOMMERCIAL PROPERTY\\n$\\nTRUCKERS\\n$\\n$\\nCRIME\\n$\\nUMBRELLA\\n$\\n$\\nATTACHMENTS\\nACCOUNTS RECEIVABLE / VALUABLE PAPERS\\nGLASS AND SIGN SECTION\\nSTATEMENT / SCHEDULE OF VALUES\\nADDITIONAL INTEREST SCHEDULE\\nHOTEL / MOTEL SUPPLEMENT\\nSTATE SUPPLEMENT (If applicable)\\nADDITIONAL PREMISES INFORMATION SCHEDULE\\nINSTALLATION / BUILDERS RISK SECTION\\nVACANT BUILDING SUPPLEMENT\\nAPARTMENT BUILDING SUPPLEMENT\\nINTERNATIONAL LIABILITY EXPOSURE SUPPLEMENT\\nVEHICLE SCHEDULE\\nCONDO ASSN BYLAWS (for D&O Coverage only)\\nINTERNATIONAL PROPERTY EXPOSURE SUPPLEMENT\\nCONTRACTORS SUPPLEMENT\\nLOSS SUMMARY\\nCOVERAGES SCHEDULE\\nOPEN CARGO SECTION\\nDEALERS SECTION\\nPREMIUM PAYMENT SUPPLEMENT\\nDRIVER INFORMATION SCHEDULE\\nPROFESSIONAL LIABILITY SUPPLEMENT\\nELECTRONIC DATA PROCESSING SECTION\\nRESTAURANT / TAVERN SUPPLEMENT\\nPOLICY INFORMATION\\nMINIMUM\\nPROPOSED EFF DATE\\nPROPOSED EXP DATE\\nBILLING PLAN\\nPAYMENT PLAN\\nMETHOD OF PAYMENT\\nAUDIT\\nDEPOSIT\\nPREMIUM\\nPOLICY PREMIUM\\n07/01/2025\\n07/01/2026\\nAGENCY\\nMO\\n$\\n$\\n$\\nDIRECT\\nAPPLICANT INFORMATION\\nNAME (First Named Insured) AND MAILING ADDRESS (including ZIP+4)\\nGL CODE\\nSIC\\nNAICS\\nFEIN OR SOC SEC #\\nMerchants Landscape Services, Inc.\\n0782\\n561730\\n95-4725606\\n1190 Monterey Pass Road\\nBUSINESS\\nPHONE\\n#: (*************\\nMonterey Park, CA 91754\\nWEBSITE ADDRESS\\nhttps://www.merchantslandscape.com/\\nCORPORATION\\nJOINT VENTURE\\nNOT FOR PROFIT ORG\\nSUBCHAPTER \"S\" CORPORATION\\nNO. OF MEMBERS\\nINDIVIDUAL\\nLLC\\nAND MANAGERS:\\nPARTNERSHIP\\nTRUST\\nClient\\nNAME (Other Named Insured) AND MAILING ADDRESS (including ZIP+4)\\nGL CODE\\nSIC\\nNAICS\\nFEIN OR SOC SEC #\\nMerchants Landscape Services, Inc.\\n97047\\n0782\\n561730\\n95-4725606\\n1190 Monterey Pass Road\\nMonterey Park, CA 91754\\nBUSINESS PHONE #:\\n************** 609\\nWEBSITE ADDRESS\\nCORPORATION\\nJOINT VENTURE\\nNOT FOR PROFIT ORG\\nSUBCHAPTER \"S\" CORPORATION\\nCommercial contractor landscaper, no r\\nNO. OF MEMBERS\\nINDIVIDUAL\\nLLC\\nAND MANAGERS:\\nPARTNERSHIP\\nTRUST\\nNAME (Other Named Insured) AND MAILING ADDRESS (including ZIP+4)\\nGL CODE\\nSIC\\nNAICS\\nFEIN OR SOC SEC #\\nMerchants Landscaping, Inc.\\n1190 Monterey Pass Road\\nMonterey Park, CA 91754\\nBUSINESS PHONE #:\\nWEBSITE ADDRESS\\nCORPORATION\\nJOINT VENTURE\\nNOT FOR PROFIT ORG\\nSUBCHAPTER \"S\" CORPORATION\\nINDIVIDUAL\\nLLC\\nNO. OF MEMBERS\\nPARTNERSHIP\\nTRUST\\nAND MANAGERS:\\nACORD 125 CA (2023/01)\\nPage 1 of 4\\n© 2022 ACORD CORPORATION. All rights reserved.\\nThe ACORD name and logo are registered marks of ACORD\\n\\n=== TABLE 1 ===\\nCOMPANY POLICY OR PROGRAM NAME,PROGRAM CODE\\r\\n,\\r\\n\\n=== TABLE 2 ===\\nUNDERWRITER,UNDERWRITER OFFICE\\r\\n,\\r\\n\\n=== TABLE 3 ===\\n,,QUOTE,,,,ISSUE,POLICY,,RENEW,\\r\\nSTATUS OF TRANSACTION,,BOUND,(Give Date,,and/or,Attach,Copy):,,,\\r\\n,,CHANGE,,DATE,,,TIME,,,AM\\r\\n,,CANCEL,,,,,,,,PM\\r\\n\\n=== TABLE 4 ===\\n,INDICATE LINES OF BUSINESS,PREMIUM\\r\\n,BOILER & MACHINERY,$\\r\\n,BUSINESS AUTO,$\\r\\n,BUSINESS OWNERS,$\\r\\n,COMMERCIAL GENERAL LIABILITY,$\\r\\n,COMMERCIAL INLAND MARINE,$\\r\\n,COMMERCIAL PROPERTY,$\\r\\n,CRIME,$\\r\\n\\n=== TABLE 5 ===\\n,,PREMIUM\\r\\n,CYBER AND PRIVACY,$\\r\\n,FIDUCIARY LIABILITY,$\\r\\n,GARAGE AND DEALERS,$\\r\\n,LIQUOR LIABILITY,$\\r\\n,MOTOR CARRIER,$\\r\\n,TRUCKERS,$\\r\\n,UMBRELLA,$\\r\\n\\n=== TABLE 6 ===\\n,,PREMIUM\\r\\n,YACHT,$\\r\\n,,$\\r\\n,,$\\r\\n,,$\\r\\n,,$\\r\\n,,$\\r\\n,,$\\r\\n\\n=== TABLE 7 ===\\n,ACCOUNTS RECEIVABLE / VALUABLE PAPERS,,GLASS AND SIGN SECTION,,STATEMENT / SCHEDULE OF VALUES\\r\\n,ADDITIONAL INTEREST SCHEDULE,,HOTEL / MOTEL SUPPLEMENT,,STATE SUPPLEMENT (If applicable)\\r\\n,ADDITIONAL PREMISES INFORMATION SCHEDULE,,INSTALLATION / BUILDERS RISK SECTION,,VACANT BUILDING SUPPLEMENT\\r\\n,APARTMENT BUILDING SUPPLEMENT,,INTERNATIONAL LIABILITY EXPOSURE SUPPLEMENT,,VEHICLE SCHEDULE\\r\\n,CONDO ASSN BYLAWS (for D&O Coverage only),,INTERNATIONAL PROPERTY EXPOSURE SUPPLEMENT,,\\r\\n,CONTRACTORS SUPPLEMENT,,LOSS SUMMARY,,\\r\\n,COVERAGES SCHEDULE,,OPEN CARGO SECTION,,\\r\\n,DEALERS SECTION,,PREMIUM PAYMENT SUPPLEMENT,,\\r\\n,DRIVER INFORMATION SCHEDULE,,PROFESSIONAL LIABILITY SUPPLEMENT,,\\r\\n,ELECTRONIC DATA PROCESSING SECTION,,RESTAURANT / TAVERN SUPPLEMENT,,\\r\\n\\n=== TABLE 8 ===\\nPROPOSED EFF DATE,PROPOSED EXP DATE,BILLING,PLAN,,PAYMENT PLAN,METHOD OF PAYMENT,AUDIT,DEPOSIT,MINIMUM PREMIUM,POLICY PREMIUM\\r\\n07/01/2025,07/01/2026,DIRECT,,AGENCY,MO,,,$,$,$\\r\\n\\n=== TABLE 9 ===\\nGL CODE,SIC,NAICS,FEIN OR SOC SEC #\\r\\n,0782,561730,95-4725606\\r\\n\\n=== TABLE 10 ===\\n,CORPORATION,,JOINT VENTURE,,NOT FOR PROFIT ORG,,\"SUBCHAPTER \"\"S\"\" CORPORATION\",,\\r\\n,INDIVIDUAL,,NO. OF MEMBERS LLC AND MANAGERS:,,PARTNERSHIP,,TRUST,,\\r\\n\\n=== TABLE 11 ===\\nGL CODE,SIC,NAICS,FEIN OR SOC SEC #\\r\\n97047,0782,561730,95-4725606\\r\\n\\n=== TABLE 12 ===\\n,CORPORATION,,JOINT VENTURE,,NOT FOR PROFIT ORG,,\"SUBCHAPTER \"\"S\"\" CORPORATION\",,\"Commercial contractor landscaper, no r\"\\r\\n,INDIVIDUAL,,NO. OF MEMBERS LLC AND MANAGERS:,,PARTNERSHIP,,TRUST,,\\r\\n\\n=== TABLE 13 ===\\nGL CODE,SIC,NAICS,FEIN OR SOC SEC #\\r\\n,,,\\r\\n\\n=== TABLE 14 ===\\n,CORPORATION,,JOINT VENTURE,,NOT FOR PROFIT ORG,,\"SUBCHAPTER \"\"S\"\" CORPORATION\",,\\r\\n,INDIVIDUAL,,LLC NO. OF MEMBERS AND MANAGERS:,,PARTNERSHIP,,TRUST,,\\r\\n'"]}, "execution_count": 170, "metadata": {}, "output_type": "execute_result"}], "source": ["# loading contents of page1_doc1_2526 TABBED APP 4 (2) (1).json\n", "structured_text"]}, {"cell_type": "code", "execution_count": 183, "id": "0b9f342d", "metadata": {}, "outputs": [], "source": ["\n", "\n", "# saving to json file\n", "with open('structured_text.json', 'w') as f:\n", "    json.dump(structured_text, f)"]}, {"cell_type": "code", "execution_count": null, "id": "173864f5", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'structured_text' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[7], line 21\u001b[0m\n\u001b[1;32m     10\u001b[0m image_format \u001b[38;5;241m=\u001b[39m image_path\u001b[38;5;241m.\u001b[39msplit(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m)[\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m]\u001b[38;5;241m.\u001b[39mlower()  \u001b[38;5;66;03m# Extract file extension (e.g., jpg)\u001b[39;00m\n\u001b[1;32m     12\u001b[0m system_prompts \u001b[38;5;241m=\u001b[39m [{\n\u001b[1;32m     13\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtext\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mExtract everything present in image in key-value pair\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m     14\u001b[0m }]\n\u001b[1;32m     16\u001b[0m messages \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m     17\u001b[0m     {\n\u001b[1;32m     18\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m<PERSON>e\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     19\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: [\n\u001b[1;32m     20\u001b[0m             {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtext\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAnalyze the provided image and describe its contents. Also read from text that is given. Convert whole content into nested key-value pairs and use tool call\u001b[39m\u001b[38;5;124m\"\u001b[39m},\n\u001b[0;32m---> 21\u001b[0m             {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtext\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[43mstructured_text\u001b[49m},\n\u001b[1;32m     22\u001b[0m             {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mimage\u001b[39m\u001b[38;5;124m\"\u001b[39m: {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mformat\u001b[39m\u001b[38;5;124m\"\u001b[39m: image_format, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msource\u001b[39m\u001b[38;5;124m\"\u001b[39m: {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mbytes\u001b[39m\u001b[38;5;124m\"\u001b[39m: image_bytes}}}\n\u001b[1;32m     23\u001b[0m         ]\n\u001b[1;32m     24\u001b[0m     }\n\u001b[1;32m     25\u001b[0m ]\n\u001b[1;32m     27\u001b[0m toolConfig\u001b[38;5;241m=\u001b[39m{\n\u001b[1;32m     28\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtools\u001b[39m\u001b[38;5;124m\"\u001b[39m: [\n\u001b[1;32m     29\u001b[0m         {\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     57\u001b[0m     }\n\u001b[1;32m     58\u001b[0m }\n\u001b[1;32m     60\u001b[0m response \u001b[38;5;241m=\u001b[39m client\u001b[38;5;241m.\u001b[39mconverse(\n\u001b[1;32m     61\u001b[0m     modelId\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124marn:aws:bedrock:us-east-1:************:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     62\u001b[0m     system\u001b[38;5;241m=\u001b[39msystem_prompts,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     70\u001b[0m \n\u001b[1;32m     71\u001b[0m )\n", "\u001b[0;31mNameError\u001b[0m: name 'structured_text' is not defined"]}], "source": ["import boto3\n", "\n", "session = boto3.Session(profile_name=\"DeveloperLearningAccountAccess-************\")\n", "client = session.client(\"bedrock-runtime\", region_name=\"us-east-1\")\n", "image_path = \"page1_doc1_2526 TABBED APP 4 (2) (1).jpeg\"\n", "\n", "with open(image_path, \"rb\") as f:\n", "    image_bytes = f.read()\n", "    \n", "image_format = image_path.split(\".\")[-1].lower()  # Extract file extension (e.g., jpg)\n", "\n", "system_prompts = [{\n", "    \"text\": \"Extract everything present in image in key-value pair\"\n", "}]\n", "\n", "messages = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": [\n", "            {\"text\": \"Analyze the provided image and describe its contents. Also read from text that is given. Convert whole content into nested key-value pairs and use tool call\"},\n", "            {\"text\": structured_text},\n", "            {\"image\": {\"format\": image_format, \"source\": {\"bytes\": image_bytes}}}\n", "        ]\n", "    }\n", "]\n", "\n", "toolConfig={\n", "    \"tools\": [\n", "        {\n", "            'toolSpec': {\n", "                'name': 'All_details',\n", "                'description': 'This tool received everything present in given image as key value pairs',\n", "                'inputSchema': {\n", "                    'json': {\n", "                        'title': 'Details',\n", "                        'type': 'object',\n", "                        'properties': {\n", "                            'content': {\n", "                                'title': 'Content',\n", "                                'type': 'string',\n", "                                'description': 'content inside the image in key-value pair',\n", "                                \"additionalProperties\": {\n", "                                        \"type\": \"string\"\n", "                                    }\n", "                            }   \n", "                        },\n", "                        'required': [\n", "                            'content'\n", "                        ]\n", "                    }\n", "                },\n", "            }\n", "        }\n", "    ],\n", "    \"toolChoice\": {\n", "        \"tool\":{\"name\":\"All_details\"}\n", "    }\n", "\n", "\n", "}\n", "\n", "response = client.converse(\n", "    modelId=\"arn:aws:bedrock:us-east-1:************:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0\",\n", "    system=system_prompts,\n", "    messages=messages,\n", "    inferenceConfig={\n", "        'maxTokens': 4096,\n", "        'temperature': 0,\n", "        'topP': 1,\n", "    },\n", "    toolConfig=toolConfig,\n", "\n", ")\n", "\n", "output = response['output']['message']['content'][0]['toolUse']['input']\n", "pprint(json.loads(output[\"content\"]))"]}, {"cell_type": "code", "execution_count": 6, "id": "6eec12a7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Pretty printing has been turned OFF\n"]}], "source": ["pprint(json.loads(output[\"content\"]))"]}, {"cell_type": "code", "execution_count": null, "id": "2329d49b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting lambda\n", "  Downloading lambda-0.0.1.tar.gz (1.6 kB)\n", "  Preparing metadata (setup.py) ... \u001b[?25ldone\n", "\u001b[?25hUsing legacy 'setup.py install' for lambda, since package 'wheel' is not installed.\n", "Installing collected packages: lambda\n", "  Running setup.py install for lambda ... \u001b[?25ldone\n", "\u001b[?25hSuccessfully installed lambda-0.0.1\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["hdierhfeirfhiwrhfiwre"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}