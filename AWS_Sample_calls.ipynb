# to login to AWS : 
!aws sso login --profile DeveloperLearningAccountAccess-************

from pprint import pprint

import boto3

# Create the Bedrock client using your SSO profile
session = boto3.Session(profile_name="DeveloperLearningAccountAccess-************")

client = session.client(
    service_name="bedrock-runtime",
    region_name="us-east-1"  # use the region where Bedrock is enabled
)

# Define the model and message
model_id = "amazon.nova-micro-v1:0"
messages = [
    {"role": "user", "content": [{"text": "Hello! Can you tell me about Amazon Bedrock?"}]}
]

# Make the API call
response = client.converse(
    modelId=model_id,
    messages=messages,
)

# Print the response
print(response['output']['message']['content'][0]['text'])


import boto3

# Create the Bedrock client using your SSO profile
session = boto3.Session(profile_name="DeveloperLearningAccountAccess-************")

client = boto3.client(
    service_name="bedrock-runtime", region_name="us-east-1",
    aws_access_key_id="********************",
    aws_secret_access_key="FmiC6arIFsm2umN5pfsJnUWzv+Gsaz8a3acSdJw9")


# Define the model and message
model_id = "amazon.nova-micro-v1:0"
messages = [
    {"role": "user", "content": [{"text": "Hello! Can you tell me about Amazon Bedrock?"}]}
]

# Make the API call
response = client.converse(
    modelId=model_id,
    messages=messages,
)

# Print the response
print(response['output']['message']['content'][0]['text'])


import boto3

client = boto3.client(
    service_name="bedrock-runtime",
    region_name="us-east-1"  # use the region where Bedrock is enabled
)

# Define the model and message
model_id = "openai.gpt-oss-20b-1:0"
messages = [
    {"role": "user", "content": [{"text": "Hello! Can you tell me about Amazon Bedrock?"}]}
]

# Make the API call
response = client.converse(
    modelId=model_id,
    messages=messages,
)

# Print the response
print(response['output']['message']['content'][0]['text'])


# Use the Conversation API to send a text message to Anthropic Claude.

import boto3
from botocore.exceptions import ClientError

# Initialize the Bedrock Runtime client
client = boto3.client("bedrock-runtime")

# Set the model ID
model_id = "openai.gpt-oss-20b-1:0"

# Set up messages and system message
messages = [
    {
        "role": "assistant", 
        "content": [
            {
                "text": "Hello! How can I help you today?"
            }
        ]
    },
    {
        "role": "user",
        "content": [
            {
                "text": "What is the weather like today?"
            }
        ]
    }
]

system = [
    {
        "text": "You are a helpful assistant."
    }
]

try:
    # Send the message to the model, using a basic inference configuration.
    response = client.converse(
        modelId=model_id,
        messages=messages,
        system=system,
        inferenceConfig={
            "maxTokens": 150, 
            "temperature": 0.7, 
            "topP": 0.9
        },
    )

    # Extract and print the response text.
    for content_block in response["output"]["message"]["content"]:
        print(content_block)

except (ClientError, Exception) as e:
    print(f"ERROR: Can't invoke '{model_id}'. Reason: {e}")
    exit(1)




pip install --upgrade boto3

import boto3
session = boto3.Session(profile_name="DeveloperLearningAccountAccess-************")
# Use 'bedrock' for listing models and profiles
bedrock_client = session.client('bedrock', region_name='us-east-1')
response = bedrock_client.list_inference_profiles()
response['inferenceProfileSummaries']

import boto3

# Create session
session = boto3.Session(profile_name="DeveloperLearningAccountAccess-************")

# IMPORTANT: Use 'bedrock-runtime' for converse method, not 'bedrock'
client = session.client(
    service_name="bedrock-runtime",  # This is the key - must be 'bedrock-runtime'
    region_name="us-east-1"
)

# System prompt
system_prompt = "You are a helpful AI assistant."

# Messages
messages = [
    {
        "role": "user",
        "content": [
            {
                "text": "Hello! Can you tell me about Amazon Bedrock?"
            }
        ]
    }
]

# Tool configuration (if needed)
toolConfig = {
    "tools": [],
    "toolChoice": {"auto": {}}
}

# Main API call
response = client.converse(
    modelId="amazon.nova-lite-v1:0",
    system=system_prompt,
    messages=messages,
    inferenceConfig={
        'maxTokens': 4096,
        'temperature': 0,
        'topP': 1,
    },
    toolConfig=toolConfig,
)

# Print the response
print(response['output']['message']['content'][0]['text'])

import boto3

session = boto3.Session(profile_name="DeveloperLearningAccountAccess-************")

client = session.client(
    service_name="textract",
    region_name="us-east-1"  # use the region where Bedrock is enabled
)

document_path = "Text_entropy.png"

# Read document bytes
with open(document_path, 'rb') as doc:
    image_bytes = doc.read()

# Call Textract
response = client.detect_document_text(
    Document={'Bytes': image_bytes}
)

# Print detected text
for item in response['Blocks']:
    if item['BlockType'] == 'LINE':
        print(item['Text'])


# Pending


    