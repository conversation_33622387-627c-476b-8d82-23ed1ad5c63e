#!/usr/bin/env python3
"""
AWS Bedrock Nova Model Usage Examples
This script demonstrates common use cases for Nova models with practical examples.
"""

import os
import boto3
from dotenv import load_dotenv

class BedrockNovaClient:
    """Simple wrapper for AWS Bedrock Nova models"""
    
    def __init__(self):
        """Initialize the client with credentials from .env file"""
        load_dotenv()
        
        aws_access_key_id = os.getenv('AWS_ACCESS_KEY_ID')
        aws_secret_access_key = os.getenv('AWS_SECRET_ACCESS_KEY')
        aws_region = os.getenv('AWS_REGION', 'us-east-1')
        
        if not aws_access_key_id or not aws_secret_access_key:
            raise ValueError("Missing AWS credentials in .env file")
        
        self.client = boto3.client(
            service_name='bedrock-runtime',
            region_name=aws_region,
            aws_access_key_id=aws_access_key_id.strip(),
            aws_secret_access_key=aws_secret_access_key.strip()
        )
        
        print(f"✓ Bedrock client initialized for region: {aws_region}")
    
    def chat(self, message, model="amazon.nova-lite-v1:0", system_prompt=None, max_tokens=500, temperature=0.7):
        """
        Simple chat interface for Nova models
        
        Args:
            message (str): User message
            model (str): Nova model ID
            system_prompt (str): Optional system prompt
            max_tokens (int): Maximum tokens to generate
            temperature (float): Temperature for randomness (0.0-1.0)
        
        Returns:
            str: Model response
        """
        messages = [
            {
                "role": "user",
                "content": [{"text": message}]
            }
        ]
        
        kwargs = {
            'modelId': model,
            'messages': messages,
            'inferenceConfig': {
                'maxTokens': max_tokens,
                'temperature': temperature,
                'topP': 0.9,
            }
        }
        
        if system_prompt:
            kwargs['system'] = [{"text": system_prompt}]
        
        try:
            response = self.client.converse(**kwargs)
            return response['output']['message']['content'][0]['text']
        except Exception as e:
            return f"Error: {e}"

def example_text_generation():
    """Example: Basic text generation"""
    print("\n" + "="*60)
    print("Example 1: Basic Text Generation")
    print("="*60)
    
    client = BedrockNovaClient()
    
    # Simple question
    response = client.chat(
        message="What are the benefits of cloud computing?",
        model="amazon.nova-micro-v1:0",
        max_tokens=200
    )
    
    print("Question: What are the benefits of cloud computing?")
    print(f"Nova Micro Response:\n{response}")

def example_code_generation():
    """Example: Code generation with system prompt"""
    print("\n" + "="*60)
    print("Example 2: Code Generation")
    print("="*60)
    
    client = BedrockNovaClient()
    
    response = client.chat(
        message="Create a Python function to validate email addresses using regex",
        model="amazon.nova-pro-v1:0",
        system_prompt="You are an expert Python developer. Provide clean, well-commented code with examples.",
        max_tokens=400,
        temperature=0.1  # Lower temperature for more consistent code
    )
    
    print("Request: Create a Python function to validate email addresses using regex")
    print(f"Nova Pro Response:\n{response}")

def example_creative_writing():
    """Example: Creative writing with higher temperature"""
    print("\n" + "="*60)
    print("Example 3: Creative Writing")
    print("="*60)
    
    client = BedrockNovaClient()
    
    response = client.chat(
        message="Write a short story about a robot learning to paint",
        model="amazon.nova-lite-v1:0",
        system_prompt="You are a creative writer who crafts engaging, imaginative stories.",
        max_tokens=300,
        temperature=0.9  # Higher temperature for creativity
    )
    
    print("Request: Write a short story about a robot learning to paint")
    print(f"Nova Lite Response:\n{response}")

def example_data_analysis():
    """Example: Data analysis and explanation"""
    print("\n" + "="*60)
    print("Example 4: Data Analysis")
    print("="*60)
    
    client = BedrockNovaClient()
    
    data_prompt = """
    Analyze this sales data and provide insights:
    
    Q1 2024: $125,000
    Q2 2024: $150,000  
    Q3 2024: $135,000
    Q4 2024: $180,000
    
    What trends do you see and what recommendations would you make?
    """
    
    response = client.chat(
        message=data_prompt,
        model="amazon.nova-lite-v1:0",
        system_prompt="You are a business analyst expert at interpreting data and providing actionable insights.",
        max_tokens=350,
        temperature=0.3
    )
    
    print("Request: Analyze sales data and provide insights")
    print(f"Nova Lite Response:\n{response}")

def example_model_comparison():
    """Example: Compare responses from different Nova models"""
    print("\n" + "="*60)
    print("Example 5: Model Comparison")
    print("="*60)
    
    client = BedrockNovaClient()
    
    question = "Explain machine learning in simple terms"
    
    models = [
        "amazon.nova-micro-v1:0",
        "amazon.nova-lite-v1:0", 
        "amazon.nova-pro-v1:0"
    ]
    
    print(f"Question: {question}\n")
    
    for model in models:
        print(f"--- {model} ---")
        response = client.chat(
            message=question,
            model=model,
            max_tokens=150,
            temperature=0.5
        )
        print(f"{response}\n")

def example_conversation_flow():
    """Example: Multi-turn conversation simulation"""
    print("\n" + "="*60)
    print("Example 6: Conversation Flow")
    print("="*60)
    
    client = BedrockNovaClient()
    
    # Simulate a conversation about Python learning
    conversation = [
        "I'm new to programming. Should I start with Python?",
        "What are the best resources to learn Python?",
        "How long does it typically take to become proficient in Python?"
    ]
    
    system_prompt = "You are a helpful programming mentor who gives practical, encouraging advice to beginners."
    
    for i, message in enumerate(conversation, 1):
        print(f"Turn {i}: {message}")
        response = client.chat(
            message=message,
            model="amazon.nova-lite-v1:0",
            system_prompt=system_prompt,
            max_tokens=200,
            temperature=0.6
        )
        print(f"Response: {response}\n")

def main():
    """Run all examples"""
    print("AWS Bedrock Nova Model Usage Examples")
    print("="*60)
    
    try:
        # Run all examples
        example_text_generation()
        example_code_generation()
        example_creative_writing()
        example_data_analysis()
        example_model_comparison()
        example_conversation_flow()
        
        print("\n" + "="*60)
        print("✓ All examples completed successfully!")
        print("✓ You can now use these patterns in your own applications.")
        print("="*60)
        
    except Exception as e:
        print(f"✗ Error running examples: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
