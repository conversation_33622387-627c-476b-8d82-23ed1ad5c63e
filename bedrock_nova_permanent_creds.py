#!/usr/bin/env python3
"""
AWS Bedrock Nova model test script using permanent AWS credentials.
This script uses only AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY from .env file.
"""

import os
import boto3
from dotenv import load_dotenv
import json

def load_aws_credentials():
    """Load AWS credentials from .env file"""
    load_dotenv()
    
    aws_access_key_id = os.getenv('AWS_ACCESS_KEY_ID')
    aws_secret_access_key = os.getenv('AWS_SECRET_ACCESS_KEY')
    aws_region = os.getenv('AWS_REGION', 'us-east-1')
    
    if not aws_access_key_id or not aws_secret_access_key:
        raise ValueError("Missing AWS_ACCESS_KEY_ID or AWS_SECRET_ACCESS_KEY in .env file")
    
    # Remove any whitespace
    aws_access_key_id = aws_access_key_id.strip()
    aws_secret_access_key = aws_secret_access_key.strip()
    
    print("✓ AWS credentials loaded from .env file")
    print(f"✓ Using region: {aws_region}")
    print(f"✓ Access Key ID: {aws_access_key_id[:8]}...")
    
    return {
        'aws_access_key_id': aws_access_key_id,
        'aws_secret_access_key': aws_secret_access_key,
        'region_name': aws_region
    }

def create_bedrock_client(credentials):
    """Create Bedrock runtime client with permanent credentials"""
    try:
        client = boto3.client(
            service_name='bedrock-runtime',
            region_name=credentials['region_name'],
            aws_access_key_id=credentials['aws_access_key_id'],
            aws_secret_access_key=credentials['aws_secret_access_key']
        )
        print("✓ Bedrock client created successfully")
        return client
    except Exception as e:
        print(f"✗ Failed to create Bedrock client: {e}")
        raise

def test_nova_micro(client):
    """Test Amazon Nova Micro model"""
    print("\n" + "="*50)
    print("Testing Amazon Nova Micro Model")
    print("="*50)
    
    model_id = "amazon.nova-micro-v1:0"
    
    messages = [
        {
            "role": "user", 
            "content": [
                {
                    "text": "Hello! Can you tell me about Amazon Bedrock in exactly 2 sentences?"
                }
            ]
        }
    ]
    
    try:
        response = client.converse(
            modelId=model_id,
            messages=messages,
            inferenceConfig={
                'maxTokens': 100,
                'temperature': 0.7,
                'topP': 0.9,
            }
        )
        
        print(f"✓ Model: {model_id}")
        print(f"✓ Response: {response['output']['message']['content'][0]['text']}")
        
        # Print usage information if available
        if 'usage' in response:
            usage = response['usage']
            print(f"✓ Input tokens: {usage.get('inputTokens', 'N/A')}")
            print(f"✓ Output tokens: {usage.get('outputTokens', 'N/A')}")
            print(f"✓ Total tokens: {usage.get('totalTokens', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error with Nova Micro: {e}")
        return False

def test_nova_lite(client):
    """Test Amazon Nova Lite model"""
    print("\n" + "="*50)
    print("Testing Amazon Nova Lite Model")
    print("="*50)
    
    model_id = "amazon.nova-lite-v1:0"
    
    messages = [
        {
            "role": "user",
            "content": [
                {
                    "text": "Explain what AWS Bedrock is and list 3 key benefits in bullet points."
                }
            ]
        }
    ]
    
    try:
        response = client.converse(
            modelId=model_id,
            messages=messages,
            inferenceConfig={
                'maxTokens': 200,
                'temperature': 0.3,
                'topP': 0.8,
            }
        )
        
        print(f"✓ Model: {model_id}")
        print(f"✓ Response: {response['output']['message']['content'][0]['text']}")
        
        # Print usage information if available
        if 'usage' in response:
            usage = response['usage']
            print(f"✓ Input tokens: {usage.get('inputTokens', 'N/A')}")
            print(f"✓ Output tokens: {usage.get('outputTokens', 'N/A')}")
            print(f"✓ Total tokens: {usage.get('totalTokens', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error with Nova Lite: {e}")
        return False

def test_nova_pro(client):
    """Test Amazon Nova Pro model"""
    print("\n" + "="*50)
    print("Testing Amazon Nova Pro Model")
    print("="*50)
    
    model_id = "amazon.nova-pro-v1:0"
    
    messages = [
        {
            "role": "user",
            "content": [
                {
                    "text": "Write a brief Python function that calculates the factorial of a number using recursion. Include comments."
                }
            ]
        }
    ]
    
    try:
        response = client.converse(
            modelId=model_id,
            messages=messages,
            inferenceConfig={
                'maxTokens': 300,
                'temperature': 0.1,
                'topP': 0.9,
            }
        )
        
        print(f"✓ Model: {model_id}")
        print(f"✓ Response: {response['output']['message']['content'][0]['text']}")
        
        # Print usage information if available
        if 'usage' in response:
            usage = response['usage']
            print(f"✓ Input tokens: {usage.get('inputTokens', 'N/A')}")
            print(f"✓ Output tokens: {usage.get('outputTokens', 'N/A')}")
            print(f"✓ Total tokens: {usage.get('totalTokens', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error with Nova Pro: {e}")
        return False

def test_conversation_with_system_prompt(client):
    """Test conversation with system prompt using Nova Lite"""
    print("\n" + "="*50)
    print("Testing Conversation with System Prompt")
    print("="*50)
    
    model_id = "amazon.nova-lite-v1:0"
    
    # System prompt as a list (required format)
    system_prompt = [
        {
            "text": "You are a helpful Python programming assistant. Always provide concise, working code examples."
        }
    ]
    
    messages = [
        {
            "role": "user",
            "content": [
                {
                    "text": "Show me how to read a JSON file in Python."
                }
            ]
        }
    ]
    
    try:
        response = client.converse(
            modelId=model_id,
            system=system_prompt,
            messages=messages,
            inferenceConfig={
                'maxTokens': 250,
                'temperature': 0.2,
                'topP': 0.9,
            }
        )
        
        print(f"✓ Model: {model_id}")
        print(f"✓ Response: {response['output']['message']['content'][0]['text']}")
        
        # Print usage information if available
        if 'usage' in response:
            usage = response['usage']
            print(f"✓ Input tokens: {usage.get('inputTokens', 'N/A')}")
            print(f"✓ Output tokens: {usage.get('outputTokens', 'N/A')}")
            print(f"✓ Total tokens: {usage.get('totalTokens', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error with system prompt test: {e}")
        return False

def main():
    """Main function to test Nova models"""
    print("AWS Bedrock Nova Model Test (Permanent Credentials)")
    print("="*60)
    
    try:
        # Load credentials
        credentials = load_aws_credentials()
        
        # Create Bedrock client
        client = create_bedrock_client(credentials)
        
        # Test different Nova models
        test_results = {}
        test_results['Nova Micro'] = test_nova_micro(client)
        test_results['Nova Lite'] = test_nova_lite(client)
        test_results['Nova Pro'] = test_nova_pro(client)
        test_results['System Prompt'] = test_conversation_with_system_prompt(client)
        
        # Summary
        print("\n" + "="*60)
        print("Test Results Summary")
        print("="*60)
        
        successful_tests = 0
        for test_name, success in test_results.items():
            status = "✓ PASSED" if success else "✗ FAILED"
            print(f"{test_name}: {status}")
            if success:
                successful_tests += 1
        
        total_tests = len(test_results)
        print(f"\nOverall: {successful_tests}/{total_tests} tests passed")
        
        if successful_tests == total_tests:
            print("🎉 All Nova model tests completed successfully!")
            print("\n✓ Your AWS Bedrock setup is working correctly!")
        elif successful_tests > 0:
            print("⚠️  Some tests passed, some failed. Check error messages above.")
        else:
            print("✗ All tests failed. Please check your AWS credentials and permissions.")
            
    except Exception as e:
        print(f"✗ Script failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
